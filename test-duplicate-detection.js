/**
 * Test script to verify duplicate transaction detection functionality
 * 
 * This script tests the storage module's ability to detect and handle duplicate transactions
 * based on date, description, and amount while preserving existing categories.
 */

// Setup testing environment
const setupTestEnv = async () => {
  // Mock localStorage
  const mockStorage = {
    storage: {},
    setItem: function(key, value) {
      this.storage[key] = value;
    },
    getItem: function(key) {
      return this.storage[key] || null;
    },
    removeItem: function(key) {
      delete this.storage[key];
    }
  };
  
  // Set up global window and localStorage for browser-like environment
  global.window = {};
  global.localStorage = mockStorage;
  
  // Dynamically import storage module - need to use dynamic import in an async context
  const storageModule = await import('./src/utils/storage.js');
  return storageModule.storage;
};

// Run the tests
const runTest = async () => {
  // Get storage module with mocked localStorage
  const storage = await setupTestEnv();

  // Test data
  const originalTransactions = [
    {
      id: 'test-1',
      date: '2025-06-01',
      description: 'Coffee Shop',
      amount: -4.50,
      category: 'Food & Dining',
      originalData: 'original-data-1'
    },
    {
      id: 'test-2',
      date: '2025-06-05',
      description: 'Gas Station',
      amount: -45.00,
      category: 'Auto & Transport',
      originalData: 'original-data-2'
    }
  ];

  // Add original transactions to storage
  storage.saveTransactions(originalTransactions);
  console.log('Original transactions added:', originalTransactions.length);

  // New transactions including duplicates and category differences
  const newTransactions = [
    {
      id: 'new-1',
      date: '2025-06-01',
      description: 'Coffee Shop',  // Duplicate but different category
      amount: -4.50,
      category: 'Miscellaneous',   // Different category
      originalData: 'new-data-1'
    },
    {
      id: 'new-2',
      date: '2025-06-05',
      description: 'Gas Station',  // Exact duplicate
      amount: -45.00,
      category: 'Auto & Transport',
      originalData: 'new-data-2'
    },
    {
      id: 'new-3',
      date: '2025-06-10',
      description: 'Grocery Store', // New transaction
      amount: -65.25,
      category: 'Groceries',
      originalData: 'new-data-3'
    }
  ];

  // Test duplicate detection
  console.log('\nTesting duplicate detection...');
  const importStats = storage.addTransactions(newTransactions);
  console.log('Import statistics:', importStats);

  // Verify storage state after import
  const finalTransactions = storage.getTransactions();
  console.log('\nFinal transactions count:', finalTransactions.length);
  console.log('Expected count:', originalTransactions.length + 1); // Only one new transaction should be added

  // Verify category preservation
  const coffeeTransaction = finalTransactions.find(t => 
    t.date === '2025-06-01' && t.description === 'Coffee Shop' && t.amount === -4.50
  );
  console.log('\nCategory preservation check:');
  console.log('Coffee transaction category:', coffeeTransaction.category);
  console.log('Expected category:', 'Food & Dining'); // Original category should be preserved

  // Test summary
  console.log('\nTest Summary:');
  console.log('Added transactions:', importStats.added);
  console.log('Expected added:', 1);
  console.log('Skipped transactions:', importStats.skipped);
  console.log('Expected skipped:', 1);
  console.log('Updated transactions (preserved categories):', importStats.updated);
  console.log('Expected updated:', 1);

  console.log('\nTest ' + 
    (importStats.added === 1 && importStats.skipped === 1 && importStats.updated === 1 && 
    coffeeTransaction.category === 'Food & Dining' ? 'PASSED ✓' : 'FAILED ✗'));
};

// Execute the test
runTest().catch(error => {
  console.error('Test failed with error:', error);
});
