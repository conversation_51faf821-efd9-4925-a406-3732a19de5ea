#!/bin/bash

# Safe Migration Script for BudgetPage
# This script safely deploys the new version alongside the existing one

# Colors for better readability
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

echo -e "\n${BLUE}==============================="
echo -e " BudgetPage Safe Migration"
echo -e "===============================${NC}\n"

# Check current status
echo -e "${BLUE}[>] Checking current deployment status${NC}"

# Check existing service
if systemctl is-active --quiet budget-insight-frontend; then
  echo -e "${GREEN}[✓] Existing service (budget-insight-frontend) is running on port 8088${NC}"
  EXISTING_SERVICE_RUNNING=true
else
  echo -e "${YELLOW}[i] No existing service detected${NC}"
  EXISTING_SERVICE_RUNNING=false
fi

# Check if new service already exists
if systemctl list-unit-files | grep -q "budgetpage.service"; then
  echo -e "${YELLOW}[i] New service (budgetpage) already exists${NC}"
  NEW_SERVICE_EXISTS=true
else
  echo -e "${BLUE}[i] New service (budgetpage) will be created${NC}"
  NEW_SERVICE_EXISTS=false
fi

# Check ports
echo -e "\n${BLUE}[>] Checking port availability${NC}"
if ss -tuln | grep -q ":8088"; then
  echo -e "${GREEN}[✓] Port 8088 in use (existing version)${NC}"
fi

if ss -tuln | grep -q ":3001"; then
  echo -e "${YELLOW}[!] Port 3001 already in use${NC}"
  echo -e "${RED}[!] Please stop any service using port 3001 before continuing${NC}"
  exit 1
else
  echo -e "${GREEN}[✓] Port 3001 available for new version${NC}"
fi

# Backup current data (if any)
echo -e "\n${BLUE}[>] Creating backup of current data${NC}"
BACKUP_DIR="./backup-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$BACKUP_DIR"

# Backup browser storage data if it exists
if [ -d "./data" ]; then
  cp -r "./data" "$BACKUP_DIR/"
  echo -e "${GREEN}[✓] Existing data directory backed up${NC}"
fi

# Backup any existing config files
for file in .env package.json; do
  if [ -f "$file" ]; then
    cp "$file" "$BACKUP_DIR/"
    echo -e "${GREEN}[✓] $file backed up${NC}"
  fi
done

echo -e "${GREEN}[✓] Backup created at: $BACKUP_DIR${NC}"

# Deploy new version
echo -e "\n${BLUE}[>] Deploying new version${NC}"
echo -e "${YELLOW}[i] This will run the enhanced build-and-deploy.sh script${NC}"
echo -e "${YELLOW}[i] The new version will run on port 3001${NC}"
echo -e "${YELLOW}[i] Your existing version on port 8088 will continue running${NC}"

read -p "Continue with deployment? [y/N]: " CONTINUE_DEPLOY
CONTINUE_DEPLOY=${CONTINUE_DEPLOY:-N}

if [[ "$CONTINUE_DEPLOY" =~ ^[Yy]$ ]]; then
  echo -e "\n${BLUE}[>] Running build-and-deploy.sh${NC}"
  
  # Make sure the script is executable
  chmod +x build-and-deploy.sh
  
  # Run the deployment script
  ./build-and-deploy.sh
  
  # Check if deployment was successful
  if systemctl is-active --quiet budgetpage; then
    echo -e "\n${GREEN}[✓] New version deployed successfully!${NC}"
    echo -e "${YELLOW}[i] Old version: http://localhost:8088${NC}"
    echo -e "${YELLOW}[i] New version: http://localhost:3001${NC}"
    
    # Test the new version
    echo -e "\n${BLUE}[>] Testing new version${NC}"
    if [ -f "./test-deployment.sh" ]; then
      chmod +x test-deployment.sh
      ./test-deployment.sh
    fi
    
    echo -e "\n${BLUE}==============================="
    echo -e " Migration Complete!"
    echo -e "===============================${NC}"
    echo -e "${GREEN}[✓] Both versions are now running:${NC}"
    echo -e "${YELLOW}• Old version (budget-insight-frontend): http://localhost:8088${NC}"
    echo -e "${YELLOW}• New version (budgetpage): http://localhost:3001${NC}"
    echo -e "${YELLOW}• Backup created at: $BACKUP_DIR${NC}"
    
    echo -e "\n${BLUE}Next Steps:${NC}"
    echo -e "${YELLOW}1. Test the new version at http://localhost:3001${NC}"
    echo -e "${YELLOW}2. Upload some CSV data to test multi-month processing${NC}"
    echo -e "${YELLOW}3. Verify data migration worked correctly${NC}"
    echo -e "${YELLOW}4. When satisfied, stop the old version:${NC}"
    echo -e "${YELLOW}   sudo systemctl stop budget-insight-frontend${NC}"
    echo -e "${YELLOW}   sudo systemctl disable budget-insight-frontend${NC}"
    
    echo -e "\n${BLUE}Service Management:${NC}"
    echo -e "${YELLOW}• New version status: sudo systemctl status budgetpage${NC}"
    echo -e "${YELLOW}• New version logs: sudo journalctl -u budgetpage -f${NC}"
    echo -e "${YELLOW}• Old version status: sudo systemctl status budget-insight-frontend${NC}"
    
  else
    echo -e "\n${RED}[✗] Deployment failed!${NC}"
    echo -e "${YELLOW}[i] Your existing version on port 8088 is still running${NC}"
    echo -e "${YELLOW}[i] Check logs: sudo journalctl -u budgetpage -f${NC}"
    echo -e "${YELLOW}[i] Backup available at: $BACKUP_DIR${NC}"
  fi
  
else
  echo -e "\n${YELLOW}[i] Deployment cancelled${NC}"
  echo -e "${YELLOW}[i] Your existing version continues running on port 8088${NC}"
  echo -e "${YELLOW}[i] Backup created at: $BACKUP_DIR (you can delete this if not needed)${NC}"
fi

echo -e "\n${BLUE}Migration script complete.${NC}"
