#!/bin/bash

# Budget Insight All-in-One Setup Script (Uptime Kuma Themed)
# This script will help you configure, build, deploy, and run the Budget Insight web app

# Exit on error
set -e

# Colors for better readability
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

clear
echo -e "\n${BLUE}==============================="
echo -e " Budget Insight Setup Wizard "
echo -e "===============================${NC}\n"

# Default ports for the application
DEFAULT_PORT=8088
DEFAULT_SERVER_PORT=3001

# Default data directory
DEFAULT_DATA_DIR="$(pwd)/data"

# Detect OS
OS=""
if [[ "$(uname)" == "Darwin" ]]; then
  OS="mac"
  echo -e "${YELLOW}[i] macOS detected${NC}"
elif [[ "$(uname)" == "Linux" ]]; then
  if [[ -f /etc/debian_version ]]; then
    OS="debian"
    echo -e "${YELLOW}[i] Debian/Ubuntu detected${NC}"
  elif [[ -f /etc/redhat-release ]]; then
    OS="rhel"
    echo -e "${YELLOW}[i] RHEL/CentOS/Fedora detected${NC}"
  else
    OS="other_linux"
    echo -e "${YELLOW}[i] Linux detected${NC}"
  fi
else
  echo -e "${RED}[!] Unsupported OS. This script works best on macOS, Debian/Ubuntu, or RHEL/CentOS/Fedora.${NC}"
  echo -e "${YELLOW}[i] You may need to install dependencies manually.${NC}"
  OS="unknown"
fi

# 1. Install Node.js and npm if not present
if ! command -v node >/dev/null 2>&1 || ! command -v npm >/dev/null 2>&1; then
  echo -e "${YELLOW}[i] Node.js/npm not found. Installing...${NC}"

  case $OS in
    mac)
      if ! command -v brew >/dev/null 2>&1; then
        echo -e "${YELLOW}[i] Installing Homebrew...${NC}"
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
      fi
      brew install node
      ;;
    debian)
      sudo apt update
      sudo apt install -y curl
      curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
      sudo apt install -y nodejs
      ;;
    rhel)
      sudo dnf install -y curl
      curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
      sudo dnf install -y nodejs
      ;;
    *)
      echo -e "${RED}[!] Unable to automatically install Node.js on this OS.${NC}"
      echo -e "${YELLOW}[i] Please install Node.js (https://nodejs.org/) manually before continuing.${NC}"
      exit 1
      ;;
  esac

  # Verify installation
  if command -v node >/dev/null 2>&1 && command -v npm >/dev/null 2>&1; then
    echo -e "${GREEN}[✓] Node.js $(node -v) and npm $(npm -v) installed successfully.${NC}"
  else
    echo -e "${RED}[!] Failed to install Node.js and npm. Please install manually.${NC}"
    exit 1
  fi
else
  echo -e "${GREEN}[✓] Node.js $(node -v) and npm $(npm -v) already installed.${NC}"
fi

# 3. Environment Configuration
ENV_FILE=".env"

echo -e "\n${BLUE}[>] Environment Configuration${NC}"

# Setup data directory
echo -e "${YELLOW}[i] Setting up data directory for file-based storage${NC}"
read -p "Enter path for data storage [default: $DEFAULT_DATA_DIR]: " DATA_DIR
DATA_DIR=${DATA_DIR:-$DEFAULT_DATA_DIR}

# Ensure data directory exists
mkdir -p "$DATA_DIR"
echo -e "${GREEN}[✓] Data directory set to: $DATA_DIR${NC}"

# Setup server port
read -p "Enter port for the backend server [default: $DEFAULT_SERVER_PORT]: " SERVER_PORT
SERVER_PORT=${SERVER_PORT:-$DEFAULT_SERVER_PORT}
echo -e "${GREEN}[✓] Server port set to: $SERVER_PORT${NC}"

# Setup API Key
if [ ! -f "$ENV_FILE" ]; then
  echo -e "\n${BLUE}[>] OpenAI API Key Setup${NC}"
  echo -e "${YELLOW}[i] You need an OpenAI API key for AI-powered transaction categorization.${NC}"
  echo -e "${YELLOW}[i] Get a key at: https://platform.openai.com/account/api-keys${NC}"
  read -p "Enter your OpenAI API key: " OPENAI_API_KEY
  
  if [ -z "$OPENAI_API_KEY" ]; then
    echo -e "${RED}[!] No API key provided. The app will not be able to categorize transactions.${NC}"
    echo -e "${YELLOW}[i] You can add it later by editing the .env file manually.${NC}"
    echo "OPENAI_API_KEY=" > $ENV_FILE
  else
    echo "OPENAI_API_KEY=$OPENAI_API_KEY" > $ENV_FILE
    echo -e "${GREEN}[✓] API key saved to .env file.${NC}"
  fi
else
  echo -e "${YELLOW}[i] .env file already exists. Checking for API key...${NC}"
  if grep -q "OPENAI_API_KEY=" "$ENV_FILE"; then
    KEY_VALUE=$(grep "OPENAI_API_KEY=" "$ENV_FILE" | cut -d'=' -f2)
    if [ -z "$KEY_VALUE" ]; then
      echo -e "${RED}[!] API key is empty in .env file.${NC}"
      echo -e "${YELLOW}[i] You may want to add it manually for transaction categorization to work.${NC}"
    else
      echo -e "${GREEN}[✓] API key found in .env file.${NC}"
    fi
  else
    echo -e "${RED}[!] No API key found in .env file.${NC}"
    echo -e "${YELLOW}[i] Adding empty API key entry. Please edit it later.${NC}"
    echo "OPENAI_API_KEY=" >> $ENV_FILE
  fi
fi

# Add server configuration to .env file
if ! grep -q "SERVER_PORT=" "$ENV_FILE"; then
  echo "SERVER_PORT=$SERVER_PORT" >> $ENV_FILE
else
  # Update existing server port
  sed -i "s/SERVER_PORT=.*/SERVER_PORT=$SERVER_PORT/" $ENV_FILE
fi

if ! grep -q "DATA_DIR=" "$ENV_FILE"; then
  echo "DATA_DIR=$DATA_DIR" >> $ENV_FILE
else
  # Update existing data directory
  sed -i "s|DATA_DIR=.*|DATA_DIR=$DATA_DIR|" $ENV_FILE
fi

echo -e "${GREEN}[✓] Environment configuration complete.${NC}"

# 4. Install dependencies
if [ -f package.json ]; then
  echo -e "\n${BLUE}[>] Installing Dependencies${NC}"
  
  # Check if npm ci is better (if package-lock.json exists)
  if [ -f package-lock.json ]; then
    echo -e "${YELLOW}[i] Installing from package-lock.json for consistency${NC}"
    npm ci
  else
    npm install
  fi
  
  # Install server dependencies
  echo -e "${YELLOW}[i] Installing server dependencies...${NC}"
  npm install --save express cors body-parser dotenv
  
  # Run npm audit fix with appropriate permissions
  echo -e "${BLUE}[>] Checking and fixing security vulnerabilities${NC}"
  if command -v sudo >/dev/null 2>&1; then
    # If sudo is available, use it to ensure proper permissions
    sudo npm audit fix --force || echo -e "${YELLOW}[!] Some vulnerabilities may require manual review${NC}"
    # Fix permissions after npm audit
    echo -e "${YELLOW}[i] Fixing permissions for node_modules${NC}"
    sudo chown -R $(whoami):$(whoami) "./node_modules"
  else
    # Try without sudo but might fail with permission errors
    npm audit fix --force || echo -e "${YELLOW}[!] Some vulnerabilities may require manual review or sudo permissions${NC}"
  fi
  
  echo -e "${GREEN}[✓] Dependencies installed successfully.${NC}"
else
  echo -e "${RED}[!] package.json not found. Please run this script from the project root (BudgetPage/project).${NC}"
  exit 1
fi

# 5. Setup Gauth SSO Secure Gateway
echo -e "\n${BLUE}[>] Security Recommendation${NC}"
echo -e "${YELLOW}[i] For production deployments, we recommend securing your application${NC}"
echo -e "${YELLOW}[i] with the Gauth SSO Secure Gateway for authentication and SSO support.${NC}"

read -p "Do you want to learn more about Gauth SSO? [y/N]: " INSTALL_GAUTH
INSTALL_GAUTH=${INSTALL_GAUTH:-N}

if [[ "$INSTALL_GAUTH" =~ ^[Yy]$ ]]; then
  echo -e "\n${BLUE}====== Gauth SSO Gateway ======${NC}"
  echo -e "${YELLOW}Features:${NC}"
  echo -e "  * Secure authentication for any web application"
  echo -e "  * Single Sign-On with Google, GitHub, etc."
  echo -e "  * Easy deployment with Docker"
  echo -e "  * Rate limiting and abuse protection"
  
  echo -e "\n${BLUE}====== Installation Options ======${NC}"
  echo -e "1. Visit https://github.com/gauth-official/gauth"
  echo -e "2. Docker: docker pull gauth/gauth:latest"
  
  echo -e "\n${YELLOW}(Shameless plug: Gauth SSO makes securing self-hosted apps simple!)${NC}"
fi

# 6. Deployment Options
echo -e "\n${BLUE}[>] Deployment Options${NC}"
echo -e "${YELLOW}[i] Choose how you want to deploy the Budget Insight application${NC}"
echo -e "1. Development mode (quick start for testing/development)"
echo -e "2. Production mode (systemd service for reliable access)"
read -p "Select an option [1-2]: " DEPLOY_OPTION
DEPLOY_OPTION=${DEPLOY_OPTION:-1}

case $DEPLOY_OPTION in
  1) # Development mode
    echo -e "\n${BLUE}[>] Starting in development mode${NC}"
    echo -e "${YELLOW}[i] The app will be available at http://localhost:5173${NC}"
    echo -e "${YELLOW}[i] Press Ctrl+C to stop the server${NC}"
    npm run dev -- --host
    ;;
    
  2) # Production mode
    echo -e "\n${BLUE}[>] Setting up production deployment${NC}"
    
    # Ask for port
    read -p "Enter the port to run the application on [default: $DEFAULT_PORT]: " APP_PORT
    APP_PORT=${APP_PORT:-$DEFAULT_PORT}
    
    # 1. Build the application
    echo -e "\n${BLUE}[>] Building application for production${NC}"
    npm run build
    echo -e "${GREEN}[✓] Build completed${NC}"

    # 2. Install a simple production server if needed
    if ! command -v serve >/dev/null 2>&1; then
      echo -e "\n${BLUE}[>] Installing production server${NC}"
      npm install -g serve
      echo -e "${GREEN}[✓] Server installed${NC}"
    fi

    # 3. Create systemd service file
    echo -e "\n${BLUE}[>] Setting up systemd service${NC}"
    SERVICE_NAME="budget-insight"
    SERVICE_PATH="/etc/systemd/system/${SERVICE_NAME}.service"

    # Get absolute paths
    APP_PATH=$(pwd)
    DIST_PATH="${APP_PATH}/dist"
    NODE_PATH=$(which node)
    SERVE_PATH=$(which serve)

    # Create service files content - one for frontend and one for backend
    NODE_SERVER_PATH="${NODE_PATH} ${APP_PATH}/server.js"
    
    FRONTEND_SERVICE="${SERVICE_NAME}-frontend"
    BACKEND_SERVICE="${SERVICE_NAME}-backend"
    
    # Frontend service
    FRONTEND_SERVICE_CONTENT="[Unit]
Description=Budget Insight Frontend Web Application
After=network.target

[Service]
Type=simple
User=$(whoami)
WorkingDirectory=${APP_PATH}
ExecStart=${SERVE_PATH} -s ${DIST_PATH} -l ${APP_PORT}
Restart=on-failure
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target"

    # Backend service
    BACKEND_SERVICE_CONTENT="[Unit]
Description=Budget Insight Backend API Server
After=network.target

[Service]
Type=simple
User=$(whoami)
WorkingDirectory=${APP_PATH}
ExecStart=${NODE_SERVER_PATH}
Restart=on-failure
Environment=NODE_ENV=production
Environment=SERVER_PORT=${SERVER_PORT}
Environment=DATA_DIR=${DATA_DIR}
EnvironmentFile=${APP_PATH}/.env

[Install]
WantedBy=multi-user.target"

    # Try to create the service files with sudo
    echo -e "${YELLOW}[i] Creating systemd service files (may require sudo password)${NC}"
    echo "$FRONTEND_SERVICE_CONTENT" > "${FRONTEND_SERVICE}.service"
    echo "$BACKEND_SERVICE_CONTENT" > "${BACKEND_SERVICE}.service"

    FRONTEND_SERVICE_PATH="/etc/systemd/system/${FRONTEND_SERVICE}.service"
    BACKEND_SERVICE_PATH="/etc/systemd/system/${BACKEND_SERVICE}.service"

    if command -v sudo >/dev/null 2>&1; then
      sudo mv "${FRONTEND_SERVICE}.service" $FRONTEND_SERVICE_PATH
      sudo mv "${BACKEND_SERVICE}.service" $BACKEND_SERVICE_PATH
      echo -e "${GREEN}[✓] Service files created:${NC}"
      echo -e "${GREEN}   - $FRONTEND_SERVICE_PATH${NC}"
      echo -e "${GREEN}   - $BACKEND_SERVICE_PATH${NC}"

      # Apply security patches with npm audit fix
      echo -e "\n${BLUE}[>] Applying security patches with npm audit fix${NC}"
      # Use sudo for npm audit fix to avoid permission issues
      if [[ -d "$APP_PATH" ]]; then
        sudo npm --prefix "$APP_PATH" audit fix --force || echo -e "${YELLOW}[!] Some vulnerabilities may require manual review${NC}"
        # Fix permissions after npm audit
        echo -e "${YELLOW}[i] Fixing permissions for node_modules${NC}"
        sudo chown -R $(whoami):$(whoami) "$APP_PATH/node_modules"
        echo -e "${GREEN}[✓] Security patches applied${NC}"
      fi

      # Enable and start the services
      echo -e "\n${BLUE}[>] Enabling and starting services${NC}"
      sudo systemctl daemon-reload
      
      # Start backend first
      sudo systemctl enable $BACKEND_SERVICE
      sudo systemctl start $BACKEND_SERVICE
      echo -e "\n${BLUE}[>] Backend service status:${NC}"
      sudo systemctl status $BACKEND_SERVICE --no-pager
      
      # Then start frontend
      sudo systemctl enable $FRONTEND_SERVICE
      sudo systemctl start $FRONTEND_SERVICE
      echo -e "\n${BLUE}[>] Frontend service status:${NC}"
      sudo systemctl status $FRONTEND_SERVICE --no-pager
      
      echo -e "\n${GREEN}[✓] Budget Insight is now running!${NC}"
      echo -e "${YELLOW}[i] Frontend available at: http://localhost:${APP_PORT}${NC}"
      echo -e "${YELLOW}[i] Backend API available at: http://localhost:${SERVER_PORT}${NC}"
      echo -e "${YELLOW}[i] To check logs:${NC}"
      echo -e "${YELLOW}   - Frontend: sudo journalctl -u ${FRONTEND_SERVICE} -f${NC}"
      echo -e "${YELLOW}   - Backend: sudo journalctl -u ${BACKEND_SERVICE} -f${NC}"
      echo -e "${YELLOW}[i] To restart services:${NC}"
      echo -e "${YELLOW}   - Frontend: sudo systemctl restart ${FRONTEND_SERVICE}${NC}"
      echo -e "${YELLOW}   - Backend: sudo systemctl restart ${BACKEND_SERVICE}${NC}"
      echo -e "${YELLOW}[i] To stop services:${NC}"
      echo -e "${YELLOW}   - Frontend: sudo systemctl stop ${FRONTEND_SERVICE}${NC}"
      echo -e "${YELLOW}   - Backend: sudo systemctl stop ${BACKEND_SERVICE}${NC}"
    else
      echo -e "${RED}[!] sudo not available. Manual installation required:${NC}"
      echo -e "${YELLOW}[i] 1. Copy ${SERVICE_NAME}.service to /etc/systemd/system/${NC}"
      echo -e "${YELLOW}[i] 2. Run: systemctl daemon-reload${NC}"
      echo -e "${YELLOW}[i] 3. Run: systemctl enable ${SERVICE_NAME}${NC}"
      echo -e "${YELLOW}[i] 4. Run: systemctl start ${SERVICE_NAME}${NC}"
      
      # Offer to run with serve directly as fallback
      read -p "Do you want to start the server directly (non-persistent)? [Y/n]: " RUN_SERVE
      RUN_SERVE=${RUN_SERVE:-Y}
      if [[ "$RUN_SERVE" =~ ^[Yy]$ ]]; then
        echo -e "\n${BLUE}[>] Starting server on port ${APP_PORT}...${NC}"
        serve -s dist -l ${APP_PORT}
      fi
    fi

    # 4. Create an Nginx configuration if desired
    read -p "Do you want to set up Nginx as a reverse proxy? [y/N]: " SETUP_NGINX
    SETUP_NGINX=${SETUP_NGINX:-N}

    if [[ "$SETUP_NGINX" =~ ^[Yy]$ ]]; then
      if command -v nginx >/dev/null 2>&1; then
        # Nginx is installed, create config
        NGINX_CONF="/etc/nginx/sites-available/${SERVICE_NAME}"
        NGINX_CONTENT="server {
    listen 80;
    server_name _;  # Change to your domain if applicable
    
    location / {
        proxy_pass http://localhost:${APP_PORT};
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
    }
}"

        echo -e "\n${BLUE}[>] Creating Nginx configuration${NC}"
        echo "$NGINX_CONTENT" > "${SERVICE_NAME}_nginx"
        
        if command -v sudo >/dev/null 2>&1; then
          sudo mv "${SERVICE_NAME}_nginx" $NGINX_CONF
          sudo ln -sf $NGINX_CONF /etc/nginx/sites-enabled/
          sudo nginx -t && sudo systemctl restart nginx
          
          echo -e "${GREEN}[✓] Nginx configured!${NC}"
          echo -e "${YELLOW}[i] You can now access the app at http://your_server_ip${NC}"
        else
          echo -e "${RED}[!] sudo not available. Manual Nginx setup required.${NC}"
          echo -e "${YELLOW}[i] See the generated ${SERVICE_NAME}_nginx file for configuration.${NC}"
        fi
      else
        echo -e "${RED}[!] Nginx is not installed.${NC}"
        echo -e "${YELLOW}[i] Install with: apt-get install nginx (Debian/Ubuntu)${NC}"
        echo -e "${YELLOW}[i] or: yum install nginx (RHEL/CentOS)${NC}"
      fi
    fi
    ;;
esac

echo -e "\n${GREEN}[✓] Setup complete!${NC}"
