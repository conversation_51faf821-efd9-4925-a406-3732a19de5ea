const express = require('express');
const fs = require('fs');
const path = require('path');
const cors = require('cors');
const bodyParser = require('body-parser');
const dotenv = require('dotenv');
const logger = require('./src/utils/errorLogger.cjs');

// Load environment variables
dotenv.config();

// Log startup information
logger.info('Starting BudgetPage server application...');

const app = express();
const PORT = process.env.SERVER_PORT || 3001;

// Define data directory - can be configured via environment variable
const DATA_DIR = process.env.DATA_DIR || path.join(__dirname, 'data');

// Create data directory if it doesn't exist
if (!fs.existsSync(DATA_DIR)) {
  fs.mkdirSync(DATA_DIR, { recursive: true });
}

// Enable CORS and JSON body parsing
app.use(cors());
app.use(bodyParser.json({ 
  limit: '50mb',
  // More relaxed JSON parsing to handle potential issues
  strict: false
}));

// File size limits for better performance and parsing
const MAX_FILE_SIZE_BYTES = 5 * 1024 * 1024; // 5MB limit for transaction files
const MAX_ARRAY_LENGTH = 1000; // Maximum number of transactions recommended

// Serve static files from the dist directory
app.use(express.static(path.join(__dirname, 'dist')));

// Endpoint to check if file storage is available
app.get('/api/storage/status', (req, res) => {
  try {
    fs.accessSync(DATA_DIR, fs.constants.R_OK | fs.constants.W_OK);
    res.json({ status: 'available', directory: DATA_DIR });
  } catch (error) {
    res.status(500).json({ status: 'unavailable', error: error.message });
  }
});

// Get all data from a specific collection (transactions, budget, history, etc.)
app.get('/api/storage/:collection', (req, res) => {
  const { collection } = req.params;
  const filePath = path.join(DATA_DIR, `${collection}.json`);
  
  try {
    if (!fs.existsSync(filePath)) {
      return res.json({ data: [] });
    }
    
    // Check file size before attempting to parse
    const stats = fs.statSync(filePath);
    if (stats.size > MAX_FILE_SIZE_BYTES) {
      logger.warn(`File size too large for ${collection}`, {
        fileSize: stats.size,
        maxSize: MAX_FILE_SIZE_BYTES,
        filePath
      });
      return res.status(413).json({
        error: 'File is too large to process',
        message: 'Please reduce the timeframe of your data and try again',
        fileSize: stats.size,
        maxSize: MAX_FILE_SIZE_BYTES
      });
    }
    
    const data = fs.readFileSync(filePath, 'utf8');
    try {
      const parsedData = JSON.parse(data);
      res.json({ data: parsedData });
    } catch (parseError) {
      logger.jsonParsingError(collection, parseError, filePath);
      // Try to sanitize and parse again
      try {
        // Handle common escape sequence issues
        const sanitizedData = data
          .replace(/\\([^"\\bfnrt/])/g, '\\\\$1') // Fix invalid escape sequences
          .replace(/([^\\])"/g, '$1\\"'); // Escape unescaped quotes
        
        const parsedData = JSON.parse(sanitizedData);
        res.json({ data: parsedData });
        
        // Save the fixed version back to the file for next time
        fs.writeFileSync(filePath, JSON.stringify(parsedData, null, 2));
        logMessage('info', `Auto-fixed and saved JSON data file for ${collection}`, {
          collection,
          filePath,
          fixedAt: new Date().toISOString()
        });
      } catch (fallbackError) {
        // Extract line and column information from error message if available
        const lineMatch = parseError.message.match(/line (\d+) column (\d+)/);
        const posMatch = parseError.message.match(/position (\d+)/);
        const position = posMatch ? parseInt(posMatch[1]) : 'unknown';
        const line = lineMatch ? parseInt(lineMatch[1]) : 'unknown';
        const column = lineMatch ? parseInt(lineMatch[2]) : 'unknown';
        
        logger.error(`JSON parse error couldn't be fixed automatically`, {
          collection,
          position,
          line,
          column
        });
        
        res.status(400).json({ 
          error: `Failed to parse JSON data: ${parseError.message}`,
          message: 'Your data file contains syntax errors. Please reduce the timeframe of your data or check for special characters in your transactions.',
          position,
          line,
          column
        });
      }
    }
  } catch (error) {
    logger.error(`Error reading ${collection}:`, {
      message: error.message,
      code: error.code,
      filePath
    });
    res.status(500).json({ error: `Failed to read ${collection} data` });
  }
});

// Save data to a specific collection
app.post('/api/storage/:collection', (req, res) => {
  const { collection } = req.params;
  const { data } = req.body;
  const filePath = path.join(DATA_DIR, `${collection}.json`);
  
  // Check data size before saving
  if (Array.isArray(data) && data.length > MAX_ARRAY_LENGTH) {
    logger.warn(`Array too large for ${collection}`, {
      arrayLength: data.length,
      maxLength: MAX_ARRAY_LENGTH
    });
    return res.status(413).json({
      error: 'Too many items to process',
      message: 'Please reduce the timeframe of your data and try again',
      count: data.length,
      maxCount: MAX_ARRAY_LENGTH
    });
  }
  
  try {
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
    res.json({ success: true });
  } catch (error) {
    logger.error(`Error writing ${collection}:`, {
      message: error.message,
      code: error.code,
      filePath
    });
    res.status(500).json({ error: `Failed to write ${collection} data` });
  }
});

// Get a monthly history file
app.get('/api/storage/history/:year/:month', (req, res) => {
  const { year, month } = req.params;
  const filePath = path.join(DATA_DIR, 'history', `${year}-${month}.json`);
  
  try {
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: 'History file not found' });
    }
    
    // Check file size before attempting to parse
    const stats = fs.statSync(filePath);
    if (stats.size > MAX_FILE_SIZE_BYTES) {
      logger.warn(`File size too large for history ${year}-${month}`, {
        fileSize: stats.size,
        maxSize: MAX_FILE_SIZE_BYTES,
        filePath
      });
      return res.status(413).json({
        error: 'File is too large to process',
        message: 'Please reduce the timeframe of your data and try again',
        fileSize: stats.size,
        maxSize: MAX_FILE_SIZE_BYTES
      });
    }
    
    const data = fs.readFileSync(filePath, 'utf8');
    try {
      const parsedData = JSON.parse(data);
      res.json({ data: parsedData });
    } catch (parseError) {
      logger.jsonParsingError(`history_${year}_${month}`, parseError, filePath);
      // Try to sanitize and parse again
      try {
        // Handle common escape sequence issues
        const sanitizedData = data
          .replace(/\\([^"\\bfnrt/])/g, '\\\\$1') // Fix invalid escape sequences
          .replace(/([^\\])"/g, '$1\\"'); // Escape unescaped quotes
        
        const parsedData = JSON.parse(sanitizedData);
        res.json({ data: parsedData });
        
        // Save the fixed version back to the file for next time
        fs.writeFileSync(filePath, JSON.stringify(parsedData, null, 2));
        logMessage('info', `Auto-fixed and saved JSON data file for ${collection}`, {
          collection,
          filePath,
          fixedAt: new Date().toISOString()
        });
      } catch (fallbackError) {
        // Extract line and column information from error message if available
        const lineMatch = parseError.message.match(/line (\d+) column (\d+)/);
        const posMatch = parseError.message.match(/position (\d+)/);
        const position = posMatch ? parseInt(posMatch[1]) : 'unknown';
        const line = lineMatch ? parseInt(lineMatch[1]) : 'unknown';
        const column = lineMatch ? parseInt(lineMatch[2]) : 'unknown';
        
        logger.error(`JSON parse error couldn't be fixed automatically for history`, {
          history: `${year}-${month}`,
          position,
          line,
          column
        });
        
        res.status(400).json({ 
          error: `Failed to parse history JSON data: ${parseError.message}`,
          message: 'Your history data file contains syntax errors. Please reduce the timeframe of your data or check for special characters in your transactions.',
          position,
          line,
          column
        });
      }
    }
  } catch (error) {
    logger.error(`Error reading history for ${year}-${month}:`, {
      message: error.message,
      code: error.code,
      filePath
    });
    res.status(500).json({ error: 'Failed to read monthly history data' });
  }
});

// Save a monthly history file
app.post('/api/storage/history/:year/:month', (req, res) => {
  const { year, month } = req.params;
  const { data } = req.body;
  
  // Check data size before saving
  if (Array.isArray(data) && data.length > MAX_ARRAY_LENGTH) {
    logger.warn(`Array too large for history ${year}-${month}`, {
      arrayLength: data.length,
      maxLength: MAX_ARRAY_LENGTH
    });
    return res.status(413).json({
      error: 'Too many items to process',
      message: 'Please reduce the timeframe of your data and try again',
      count: data.length,
      maxCount: MAX_ARRAY_LENGTH
    });
  }
  
  // Create history directory if it doesn't exist
  const historyDir = path.join(DATA_DIR, 'history');
  if (!fs.existsSync(historyDir)) {
    fs.mkdirSync(historyDir, { recursive: true });
  }
  
  const filePath = path.join(historyDir, `${year}-${month}.json`);
  
  try {
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
    res.json({ success: true });
  } catch (error) {
    logger.error(`Error writing history for ${year}-${month}:`, {
      message: error.message,
      code: error.code,
      filePath
    });
    res.status(500).json({ error: 'Failed to write monthly history data' });
  }
});

// Delete a monthly history file
app.delete('/api/storage/history/:year/:month', (req, res) => {
  const { year, month } = req.params;
  const filePath = path.join(DATA_DIR, 'history', `${year}-${month}.json`);
  
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
    res.json({ success: true });
  } catch (error) {
    logger.error(`Error deleting history for ${year}-${month}:`, {
      message: error.message,
      code: error.code,
      filePath
    });
    res.status(500).json({ error: 'Failed to delete monthly history data' });
  }
});

// Clear all data (admin function)
app.post('/api/storage/clear', (req, res) => {
  try {
    const files = fs.readdirSync(DATA_DIR);
    files.forEach(file => {
      if (file.endsWith('.json')) {
        fs.unlinkSync(path.join(DATA_DIR, file));
      }
    });
    
    // Also clear history directory if it exists
    const historyDir = path.join(DATA_DIR, 'history');
    if (fs.existsSync(historyDir)) {
      const historyFiles = fs.readdirSync(historyDir);
      historyFiles.forEach(file => {
        fs.unlinkSync(path.join(historyDir, file));
      });
    }
    
    res.json({ success: true });
  } catch (error) {
    logger.error('Error clearing all data:', {
      message: error.message,
      code: error.code,
      directory: DATA_DIR
    });
    res.status(500).json({ error: 'Failed to clear all data' });
  }
});

// Serve the main index.html for all routes (SPA support)
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

// Return OpenAI API key from environment (if it exists)
app.get('/api/config/openai', (req, res) => {
  const apiKey = process.env.OPENAI_API_KEY || '';
  res.json({ apiKey });
});

// Server status endpoint that includes JSON parsing error statistics
app.get('/api/status', (req, res) => {
  res.json({
    status: 'online',
    startTime: process.uptime(),
    jsonErrors: logger.jsonErrors.getSummary()
  });
});

// Start the server
app.listen(PORT, () => {
  logger.info(`Server running on port ${PORT}`);
  logger.info(`Data directory: ${DATA_DIR}`);
  logger.info(`API key configured: ${process.env.OPENAI_API_KEY ? 'Yes' : 'No'}`);
});
