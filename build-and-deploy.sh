#!/bin/bash

# BudgetPage Build and Deploy Script
# This script builds the app for production and sets up the full-stack server

# Exit on error
set -e

# Colors for better readability
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

echo -e "\n${BLUE}==============================="
echo -e " BudgetPage Production Setup"
echo -e "===============================${NC}\n"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
  echo -e "${RED}[!] package.json not found. Please run this script from the project directory.${NC}"
  exit 1
fi

# Check if server.cjs exists
if [ ! -f "server.cjs" ]; then
  echo -e "${RED}[!] server.cjs not found. Please ensure the backend server file exists.${NC}"
  exit 1
fi

# Check Node.js version
NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
  echo -e "${RED}[!] Node.js 18+ is required. Current version: $(node --version)${NC}"
  exit 1
fi

# 1. Install dependencies
echo -e "\n${BLUE}[>] Installing dependencies${NC}"
npm install
echo -e "${GREEN}[✓] Dependencies installed${NC}"

# 2. Build the application
echo -e "\n${BLUE}[>] Building application for production${NC}"
npm run build
echo -e "${GREEN}[✓] Build completed${NC}"

# 3. Set up data directory
echo -e "\n${BLUE}[>] Setting up data directory${NC}"
DATA_DIR="${PWD}/data"
if [ ! -d "$DATA_DIR" ]; then
  mkdir -p "$DATA_DIR"
  echo -e "${GREEN}[✓] Data directory created at $DATA_DIR${NC}"
else
  echo -e "${YELLOW}[i] Data directory already exists at $DATA_DIR${NC}"
fi

# Set proper permissions for data directory
chmod 755 "$DATA_DIR"
echo -e "${GREEN}[✓] Data directory permissions set${NC}"

# 4. Set up environment configuration
echo -e "\n${BLUE}[>] Setting up environment configuration${NC}"
ENV_FILE="${PWD}/.env"
if [ ! -f "$ENV_FILE" ]; then
  cat > "$ENV_FILE" << EOF
# BudgetPage Production Configuration
NODE_ENV=production
PORT=3001
DATA_DIR=${DATA_DIR}
# OPENAI_API_KEY=your_openai_api_key_here
EOF
  echo -e "${GREEN}[✓] Environment file created at $ENV_FILE${NC}"
  echo -e "${YELLOW}[i] Please edit $ENV_FILE to add your OpenAI API key if needed${NC}"
else
  echo -e "${YELLOW}[i] Environment file already exists at $ENV_FILE${NC}"
fi

# 5. Create systemd service file
echo -e "\n${BLUE}[>] Setting up systemd service${NC}"
SERVICE_NAME="budgetpage"
SERVICE_PATH="/etc/systemd/system/${SERVICE_NAME}.service"

# Get absolute paths
APP_PATH=$(pwd)
NODE_PATH=$(which node)
SERVER_FILE="${APP_PATH}/server.cjs"

# Create service file content
SERVICE_CONTENT="[Unit]
Description=BudgetPage Personal Budget Management Application
After=network.target

[Service]
Type=simple
User=$(whoami)
WorkingDirectory=${APP_PATH}
ExecStart=${NODE_PATH} ${SERVER_FILE}
Restart=on-failure
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=3001
Environment=DATA_DIR=${DATA_DIR}
EnvironmentFile=${ENV_FILE}

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=${DATA_DIR}
ReadWritePaths=${APP_PATH}/logs

[Install]
WantedBy=multi-user.target"

# Create logs directory
LOGS_DIR="${APP_PATH}/logs"
if [ ! -d "$LOGS_DIR" ]; then
  mkdir -p "$LOGS_DIR"
  echo -e "${GREEN}[✓] Logs directory created at $LOGS_DIR${NC}"
fi

# Try to create the service file with sudo
echo -e "${YELLOW}[i] Creating systemd service file (may require sudo password)${NC}"
echo "$SERVICE_CONTENT" > "${SERVICE_NAME}.service"

if command -v sudo >/dev/null 2>&1; then
  sudo mv "${SERVICE_NAME}.service" $SERVICE_PATH
  echo -e "${GREEN}[✓] Service file created at $SERVICE_PATH${NC}"

  # Enable and start the service
  echo -e "\n${BLUE}[>] Enabling and starting service${NC}"
  sudo systemctl daemon-reload
  sudo systemctl enable $SERVICE_NAME

  # Stop service if it's already running
  if sudo systemctl is-active --quiet $SERVICE_NAME; then
    echo -e "${YELLOW}[i] Stopping existing service${NC}"
    sudo systemctl stop $SERVICE_NAME
  fi

  sudo systemctl start $SERVICE_NAME

  # Wait a moment for service to start
  sleep 2

  # Check status
  echo -e "\n${BLUE}[>] Service status:${NC}"
  sudo systemctl status $SERVICE_NAME --no-pager

  echo -e "\n${GREEN}[✓] BudgetPage is now running as a service!${NC}"
  echo -e "${YELLOW}[i] Frontend: http://localhost:3001${NC}"
  echo -e "${YELLOW}[i] API: http://localhost:3001/api${NC}"
  echo -e "${YELLOW}[i] Data directory: $DATA_DIR${NC}"
  echo -e "${YELLOW}[i] To check logs: sudo journalctl -u ${SERVICE_NAME} -f${NC}"
  echo -e "${YELLOW}[i] To restart: sudo systemctl restart ${SERVICE_NAME}${NC}"
  echo -e "${YELLOW}[i] To stop: sudo systemctl stop ${SERVICE_NAME}${NC}"
else
  echo -e "${RED}[!] sudo not available. Manual installation required:${NC}"
  echo -e "${YELLOW}[i] 1. Copy ${SERVICE_NAME}.service to /etc/systemd/system/${NC}"
  echo -e "${YELLOW}[i] 2. Run: systemctl daemon-reload${NC}"
  echo -e "${YELLOW}[i] 3. Run: systemctl enable ${SERVICE_NAME}${NC}"
  echo -e "${YELLOW}[i] 4. Run: systemctl start ${SERVICE_NAME}${NC}"
fi

# 6. Create an Nginx configuration if desired
read -p "Do you want to set up Nginx as a reverse proxy? [y/N]: " SETUP_NGINX
SETUP_NGINX=${SETUP_NGINX:-N}

if [[ "$SETUP_NGINX" =~ ^[Yy]$ ]]; then
  if command -v nginx >/dev/null 2>&1; then
    # Nginx is installed, create config
    NGINX_CONF="/etc/nginx/sites-available/${SERVICE_NAME}"
    NGINX_CONTENT="server {
    listen 80;
    server_name _;  # Change to your domain if applicable

    # Increase client max body size for CSV uploads
    client_max_body_size 10M;

    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;

        # Timeout settings for large file uploads
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # API specific settings
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;

        # Longer timeout for AI processing
        proxy_connect_timeout 120s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;
    }
}"

    echo -e "\n${BLUE}[>] Creating Nginx configuration${NC}"
    echo "$NGINX_CONTENT" > "${SERVICE_NAME}_nginx"

    if command -v sudo >/dev/null 2>&1; then
      sudo mv "${SERVICE_NAME}_nginx" $NGINX_CONF
      sudo ln -sf $NGINX_CONF /etc/nginx/sites-enabled/
      sudo nginx -t && sudo systemctl restart nginx

      echo -e "${GREEN}[✓] Nginx configured!${NC}"
      echo -e "${YELLOW}[i] You can now access the app at http://your_server_ip${NC}"
      echo -e "${YELLOW}[i] Direct access still available at http://your_server_ip:3001${NC}"
    else
      echo -e "${RED}[!] sudo not available. Manual Nginx setup required.${NC}"
      echo -e "${YELLOW}[i] See the generated ${SERVICE_NAME}_nginx file for configuration.${NC}"
    fi
  else
    echo -e "${RED}[!] Nginx is not installed.${NC}"
    echo -e "${YELLOW}[i] Install with: apt-get install nginx (Debian/Ubuntu)${NC}"
    echo -e "${YELLOW}[i] or: yum install nginx (RHEL/CentOS)${NC}"
  fi
fi

# 7. Final setup summary
echo -e "\n${GREEN}==============================="
echo -e " Production Setup Complete!"
echo -e "===============================${NC}"
echo -e "${GREEN}[✓] Application built and deployed${NC}"
echo -e "${GREEN}[✓] Data directory configured: $DATA_DIR${NC}"
echo -e "${GREEN}[✓] Environment file created: $ENV_FILE${NC}"
echo -e "${GREEN}[✓] Systemd service installed and running${NC}"

echo -e "\n${BLUE}Next Steps:${NC}"
echo -e "${YELLOW}1. Edit $ENV_FILE to add your OpenAI API key (optional)${NC}"
echo -e "${YELLOW}2. Access the application at http://localhost:3001${NC}"
echo -e "${YELLOW}3. Upload your bank CSV files to test multi-month processing${NC}"
echo -e "${YELLOW}4. Check service logs: sudo journalctl -u ${SERVICE_NAME} -f${NC}"

echo -e "\n${BLUE}Service Management:${NC}"
echo -e "${YELLOW}• Start: sudo systemctl start ${SERVICE_NAME}${NC}"
echo -e "${YELLOW}• Stop: sudo systemctl stop ${SERVICE_NAME}${NC}"
echo -e "${YELLOW}• Restart: sudo systemctl restart ${SERVICE_NAME}${NC}"
echo -e "${YELLOW}• Status: sudo systemctl status ${SERVICE_NAME}${NC}"
echo -e "${YELLOW}• Logs: sudo journalctl -u ${SERVICE_NAME} -f${NC}"

echo -e "\n${GREEN}BudgetPage is ready for production use!${NC}"
