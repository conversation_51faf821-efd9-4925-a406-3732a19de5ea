#!/bin/bash

# Budget Insight Build and Deploy Script
# This script builds the app for production and sets up a server

# Exit on error
set -e

# Colors for better readability
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

echo -e "\n${BLUE}==============================="
echo -e " Budget Insight Production Setup"
echo -e "===============================${NC}\n"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
  echo -e "${RED}[!] package.json not found. Please run this script from the project directory.${NC}"
  exit 1
fi

# 1. Build the application
echo -e "\n${BLUE}[>] Building application for production${NC}"
npm run build
echo -e "${GREEN}[✓] Build completed${NC}"

# 2. Install a simple production server if needed
if ! command -v serve >/dev/null 2>&1; then
  echo -e "\n${BLUE}[>] Installing production server${NC}"
  npm install -g serve
  echo -e "${GREEN}[✓] Server installed${NC}"
fi

# 3. Create systemd service file
echo -e "\n${BLUE}[>] Setting up systemd service${NC}"
SERVICE_NAME="budget-insight"
SERVICE_PATH="/etc/systemd/system/${SERVICE_NAME}.service"

# Get absolute paths
APP_PATH=$(pwd)
DIST_PATH="${APP_PATH}/dist"
NODE_PATH=$(which node)
SERVE_PATH=$(which serve)

# Create service file content
SERVICE_CONTENT="[Unit]
Description=Budget Insight Web Application
After=network.target

[Service]
Type=simple
User=$(whoami)
WorkingDirectory=${APP_PATH}
ExecStart=${SERVE_PATH} -s ${DIST_PATH} -l 8080
Restart=on-failure
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target"

# Try to create the service file with sudo
echo -e "${YELLOW}[i] Creating systemd service file (may require sudo password)${NC}"
echo "$SERVICE_CONTENT" > "${SERVICE_NAME}.service"

if command -v sudo >/dev/null 2>&1; then
  sudo mv "${SERVICE_NAME}.service" $SERVICE_PATH
  echo -e "${GREEN}[✓] Service file created at $SERVICE_PATH${NC}"

  # Enable and start the service
  echo -e "\n${BLUE}[>] Enabling and starting service${NC}"
  sudo systemctl daemon-reload
  sudo systemctl enable $SERVICE_NAME
  sudo systemctl start $SERVICE_NAME
  
  # Check status
  echo -e "\n${BLUE}[>] Service status:${NC}"
  sudo systemctl status $SERVICE_NAME --no-pager
  
  echo -e "\n${GREEN}[✓] Budget Insight is now running as a service!${NC}"
  echo -e "${YELLOW}[i] You can access it at: http://localhost:8080${NC}"
  echo -e "${YELLOW}[i] To check logs: sudo journalctl -u ${SERVICE_NAME} -f${NC}"
  echo -e "${YELLOW}[i] To restart: sudo systemctl restart ${SERVICE_NAME}${NC}"
  echo -e "${YELLOW}[i] To stop: sudo systemctl stop ${SERVICE_NAME}${NC}"
else
  echo -e "${RED}[!] sudo not available. Manual installation required:${NC}"
  echo -e "${YELLOW}[i] 1. Copy ${SERVICE_NAME}.service to /etc/systemd/system/${NC}"
  echo -e "${YELLOW}[i] 2. Run: systemctl daemon-reload${NC}"
  echo -e "${YELLOW}[i] 3. Run: systemctl enable ${SERVICE_NAME}${NC}"
  echo -e "${YELLOW}[i] 4. Run: systemctl start ${SERVICE_NAME}${NC}"
fi

# 4. Create an Nginx configuration if desired
read -p "Do you want to set up Nginx as a reverse proxy? [y/N]: " SETUP_NGINX
SETUP_NGINX=${SETUP_NGINX:-N}

if [[ "$SETUP_NGINX" =~ ^[Yy]$ ]]; then
  if command -v nginx >/dev/null 2>&1; then
    # Nginx is installed, create config
    NGINX_CONF="/etc/nginx/sites-available/${SERVICE_NAME}"
    NGINX_CONTENT="server {
    listen 80;
    server_name _;  # Change to your domain if applicable
    
    location / {
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
    }
}"

    echo -e "\n${BLUE}[>] Creating Nginx configuration${NC}"
    echo "$NGINX_CONTENT" > "${SERVICE_NAME}_nginx"
    
    if command -v sudo >/dev/null 2>&1; then
      sudo mv "${SERVICE_NAME}_nginx" $NGINX_CONF
      sudo ln -sf $NGINX_CONF /etc/nginx/sites-enabled/
      sudo nginx -t && sudo systemctl restart nginx
      
      echo -e "${GREEN}[✓] Nginx configured!${NC}"
      echo -e "${YELLOW}[i] You can now access the app at http://your_server_ip${NC}"
    else
      echo -e "${RED}[!] sudo not available. Manual Nginx setup required.${NC}"
      echo -e "${YELLOW}[i] See the generated ${SERVICE_NAME}_nginx file for configuration.${NC}"
    fi
  else
    echo -e "${RED}[!] Nginx is not installed.${NC}"
    echo -e "${YELLOW}[i] Install with: apt-get install nginx (Debian/Ubuntu)${NC}"
    echo -e "${YELLOW}[i] or: yum install nginx (RHEL/CentOS)${NC}"
  fi
fi

echo -e "\n${GREEN}[✓] Production setup complete!${NC}"
