#!/bin/bash

# FocusBudget Build and Deploy Script
# This script builds the app for production and sets up the full-stack server

# Exit on error
set -e

# Colors for better readability
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

echo -e "\n${BLUE}==============================="
echo -e " FocusBudget Production Setup"
echo -e "===============================${NC}\n"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
  echo -e "${RED}[!] package.json not found. Please run this script from the project directory.${NC}"
  echo -e "${YELLOW}[i] Current directory: $(pwd)${NC}"
  echo -e "${YELLOW}[i] Expected files: package.json, server.cjs, src/App.tsx${NC}"
  exit 1
fi

# Check if server.cjs exists
if [ ! -f "server.cjs" ]; then
  echo -e "${RED}[!] server.cjs not found. Please ensure the backend server file exists.${NC}"
  echo -e "${YELLOW}[i] Looking for server.cjs in: $(pwd)${NC}"
  exit 1
fi

# Check if src directory exists (React app structure)
if [ ! -d "src" ]; then
  echo -e "${RED}[!] src directory not found. This doesn't appear to be a React project.${NC}"
  echo -e "${YELLOW}[i] Current directory: $(pwd)${NC}"
  echo -e "${YELLOW}[i] Contents: $(ls -la)${NC}"
  exit 1
fi

echo -e "${GREEN}[✓] Project structure validated${NC}"

# Check if Node.js is installed
if ! command -v node >/dev/null 2>&1; then
  echo -e "${RED}[!] Node.js is not installed or not in PATH${NC}"
  echo -e "${YELLOW}[i] Please install Node.js 18+ from https://nodejs.org/${NC}"
  echo -e "${YELLOW}[i] Or use a package manager:${NC}"
  echo -e "${YELLOW}    • Ubuntu/Debian: curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash - && sudo apt-get install -y nodejs${NC}"
  echo -e "${YELLOW}    • RHEL/CentOS: curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash - && sudo yum install -y nodejs${NC}"
  echo -e "${YELLOW}    • macOS: brew install node${NC}"
  exit 1
fi

# Check if npm is installed
if ! command -v npm >/dev/null 2>&1; then
  echo -e "${RED}[!] npm is not installed or not in PATH${NC}"
  echo -e "${YELLOW}[i] npm usually comes with Node.js. Please reinstall Node.js from https://nodejs.org/${NC}"
  exit 1
fi

# Check Node.js version
NODE_VERSION=$(node --version 2>/dev/null | cut -d'v' -f2 | cut -d'.' -f1)
if [ -z "$NODE_VERSION" ] || [ "$NODE_VERSION" -lt 18 ]; then
  echo -e "${RED}[!] Node.js 18+ is required. Current version: $(node --version 2>/dev/null || echo 'unknown')${NC}"
  echo -e "${YELLOW}[i] Please update Node.js to version 18 or higher${NC}"
  exit 1
fi

echo -e "${GREEN}[✓] Node.js $(node --version) and npm $(npm --version) are available${NC}"

# 1. Install dependencies
echo -e "\n${BLUE}[>] Installing dependencies${NC}"
npm install
echo -e "${GREEN}[✓] Dependencies installed${NC}"

# 2. Build the application
echo -e "\n${BLUE}[>] Building application for production${NC}"
npm run build
echo -e "${GREEN}[✓] Build completed${NC}"

# 3. Set up data directory
echo -e "\n${BLUE}[>] Setting up data directory${NC}"
DATA_DIR="${PWD}/data"
if [ ! -d "$DATA_DIR" ]; then
  mkdir -p "$DATA_DIR"
  echo -e "${GREEN}[✓] Data directory created at $DATA_DIR${NC}"
else
  echo -e "${YELLOW}[i] Data directory already exists at $DATA_DIR${NC}"
fi

# Set proper permissions for data directory
chmod 755 "$DATA_DIR"
echo -e "${GREEN}[✓] Data directory permissions set${NC}"

# 4. Set up environment configuration
echo -e "\n${BLUE}[>] Setting up environment configuration${NC}"
ENV_FILE="${PWD}/.env"
if [ ! -f "$ENV_FILE" ]; then
  cat > "$ENV_FILE" << EOF
# FocusBudget Production Configuration
NODE_ENV=production
PORT=3001
DATA_DIR=${DATA_DIR}
# OPENAI_API_KEY=your_openai_api_key_here
EOF
  echo -e "${GREEN}[✓] Environment file created at $ENV_FILE${NC}"
  echo -e "${YELLOW}[i] Please edit $ENV_FILE to add your OpenAI API key if needed${NC}"
else
  echo -e "${YELLOW}[i] Environment file already exists at $ENV_FILE${NC}"
fi

# 5. Create systemd service file
echo -e "\n${BLUE}[>] Setting up systemd service${NC}"
SERVICE_NAME="focusbudget"
SERVICE_PATH="/etc/systemd/system/${SERVICE_NAME}.service"

# Get absolute paths
APP_PATH=$(pwd)
NODE_PATH=$(which node)
SERVER_FILE="${APP_PATH}/server.cjs"

# Create service file content
SERVICE_CONTENT="[Unit]
Description=FocusBudget Personal Budget Management Application
After=network.target

[Service]
Type=simple
User=$(whoami)
WorkingDirectory=${APP_PATH}
ExecStart=${NODE_PATH} ${SERVER_FILE}
Restart=on-failure
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=3001
Environment=DATA_DIR=${DATA_DIR}
EnvironmentFile=${ENV_FILE}

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=${DATA_DIR}
ReadWritePaths=${APP_PATH}/logs

[Install]
WantedBy=multi-user.target"

# Create logs directory
LOGS_DIR="${APP_PATH}/logs"
if [ ! -d "$LOGS_DIR" ]; then
  mkdir -p "$LOGS_DIR"
  echo -e "${GREEN}[✓] Logs directory created at $LOGS_DIR${NC}"
fi

# Try to create the service file with sudo
echo -e "${YELLOW}[i] Creating systemd service file (may require sudo password)${NC}"
echo "$SERVICE_CONTENT" > "${SERVICE_NAME}.service"

if command -v sudo >/dev/null 2>&1; then
  sudo mv "${SERVICE_NAME}.service" $SERVICE_PATH
  echo -e "${GREEN}[✓] Service file created at $SERVICE_PATH${NC}"

  # Enable and start the service
  echo -e "\n${BLUE}[>] Enabling and starting service${NC}"
  sudo systemctl daemon-reload
  sudo systemctl enable $SERVICE_NAME

  # Stop service if it's already running
  if sudo systemctl is-active --quiet $SERVICE_NAME; then
    echo -e "${YELLOW}[i] Stopping existing service${NC}"
    sudo systemctl stop $SERVICE_NAME
  fi

  sudo systemctl start $SERVICE_NAME

  # Wait a moment for service to start
  sleep 2

  # Check status
  echo -e "\n${BLUE}[>] Service status:${NC}"
  sudo systemctl status $SERVICE_NAME --no-pager

  echo -e "\n${GREEN}[✓] FocusBudget is now running as a service!${NC}"
  echo -e "${YELLOW}[i] Frontend: http://localhost:3001${NC}"
  echo -e "${YELLOW}[i] API: http://localhost:3001/api${NC}"
  echo -e "${YELLOW}[i] Data directory: $DATA_DIR${NC}"
  echo -e "${YELLOW}[i] To check logs: sudo journalctl -u ${SERVICE_NAME} -f${NC}"
  echo -e "${YELLOW}[i] To restart: sudo systemctl restart ${SERVICE_NAME}${NC}"
  echo -e "${YELLOW}[i] To stop: sudo systemctl stop ${SERVICE_NAME}${NC}"
else
  echo -e "${RED}[!] sudo not available. Manual installation required:${NC}"
  echo -e "${YELLOW}[i] 1. Copy ${SERVICE_NAME}.service to /etc/systemd/system/${NC}"
  echo -e "${YELLOW}[i] 2. Run: systemctl daemon-reload${NC}"
  echo -e "${YELLOW}[i] 3. Run: systemctl enable ${SERVICE_NAME}${NC}"
  echo -e "${YELLOW}[i] 4. Run: systemctl start ${SERVICE_NAME}${NC}"
fi

# 6. Set up Nginx reverse proxy automatically
echo -e "\n${BLUE}[>] Setting up Nginx reverse proxy${NC}"
# Check if Nginx is installed, if not, install it
if ! command -v nginx >/dev/null 2>&1; then
  echo -e "${YELLOW}[i] Nginx not found, attempting to install...${NC}"

  # Detect package manager and install Nginx
  if command -v apt-get >/dev/null 2>&1; then
    echo -e "${BLUE}[>] Installing Nginx using apt-get${NC}"
    sudo apt-get update
    sudo apt-get install -y nginx
  elif command -v yum >/dev/null 2>&1; then
    echo -e "${BLUE}[>] Installing Nginx using yum${NC}"
    sudo yum install -y nginx
  elif command -v dnf >/dev/null 2>&1; then
    echo -e "${BLUE}[>] Installing Nginx using dnf${NC}"
    sudo dnf install -y nginx
  elif command -v pacman >/dev/null 2>&1; then
    echo -e "${BLUE}[>] Installing Nginx using pacman${NC}"
    sudo pacman -S --noconfirm nginx
  else
    echo -e "${RED}[!] Could not detect package manager. Please install Nginx manually.${NC}"
    echo -e "${YELLOW}[i] Debian/Ubuntu: sudo apt-get install nginx${NC}"
    echo -e "${YELLOW}[i] RHEL/CentOS: sudo yum install nginx${NC}"
    echo -e "${YELLOW}[i] Fedora: sudo dnf install nginx${NC}"
    echo -e "${YELLOW}[i] Arch: sudo pacman -S nginx${NC}"
    exit 1
  fi

  if command -v nginx >/dev/null 2>&1; then
    echo -e "${GREEN}[✓] Nginx installed successfully${NC}"
  else
    echo -e "${RED}[!] Nginx installation failed${NC}"
    exit 1
  fi
else
  echo -e "${GREEN}[✓] Nginx is already installed${NC}"
fi

# Create Nginx configuration
NGINX_CONF="/etc/nginx/sites-available/${SERVICE_NAME}"
NGINX_ENABLED="/etc/nginx/sites-enabled/${SERVICE_NAME}"

# Check if sites-available directory exists (some distros use different structure)
if [ ! -d "/etc/nginx/sites-available" ]; then
  echo -e "${YELLOW}[i] Creating sites-available directory${NC}"
  sudo mkdir -p /etc/nginx/sites-available
fi

if [ ! -d "/etc/nginx/sites-enabled" ]; then
  echo -e "${YELLOW}[i] Creating sites-enabled directory${NC}"
  sudo mkdir -p /etc/nginx/sites-enabled
fi

# Enhanced Nginx configuration
NGINX_CONTENT="# FocusBudget Nginx Configuration
# Generated by build-and-deploy.sh

server {
    listen 80;
    listen [::]:80;
    server_name _;  # Change to your domain if applicable

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection \"1; mode=block\";
    add_header Referrer-Policy strict-origin-when-cross-origin;

    # Increase client max body size for CSV uploads
    client_max_body_size 10M;

    # Main application proxy
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;

        # Timeout settings for large file uploads
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
    }

    # API specific settings with longer timeouts
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;

        # Extended timeout for AI processing
        proxy_connect_timeout 120s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;

        # Disable buffering for API responses
        proxy_buffering off;
    }

    # Static assets caching (if served directly by Nginx in future)
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://localhost:3001;
        proxy_set_header Host \$host;
        expires 1y;
        add_header Cache-Control \"public, immutable\";
    }

    # Health check endpoint
    location /health {
        proxy_pass http://localhost:3001/api/status;
        proxy_set_header Host \$host;
        access_log off;
    }
}"

echo -e "${BLUE}[>] Creating Nginx configuration${NC}"
echo "$NGINX_CONTENT" > "${SERVICE_NAME}_nginx.conf"

if command -v sudo >/dev/null 2>&1; then
  # Remove any existing configuration
  if [ -f "$NGINX_ENABLED" ]; then
    echo -e "${YELLOW}[i] Removing existing Nginx configuration${NC}"
    sudo rm -f "$NGINX_ENABLED"
  fi

  # Install new configuration
  sudo mv "${SERVICE_NAME}_nginx.conf" "$NGINX_CONF"
  sudo ln -sf "$NGINX_CONF" "$NGINX_ENABLED"

  # Test Nginx configuration
  echo -e "${BLUE}[>] Testing Nginx configuration${NC}"
  if sudo nginx -t; then
    echo -e "${GREEN}[✓] Nginx configuration is valid${NC}"

    # Enable and start Nginx
    echo -e "${BLUE}[>] Starting Nginx service${NC}"
    sudo systemctl enable nginx
    sudo systemctl restart nginx

    # Check if Nginx is running
    if systemctl is-active --quiet nginx; then
      echo -e "${GREEN}[✓] Nginx is running and configured!${NC}"
      echo -e "${YELLOW}[i] FocusBudget is now accessible at:${NC}"
      echo -e "${YELLOW}  • http://localhost (port 80 via Nginx)${NC}"
      echo -e "${YELLOW}  • http://your_server_ip (port 80 via Nginx)${NC}"
      echo -e "${YELLOW}  • http://localhost:3001 (direct access)${NC}"
    else
      echo -e "${RED}[!] Nginx failed to start${NC}"
      echo -e "${YELLOW}[i] Check Nginx logs: sudo journalctl -u nginx -f${NC}"
    fi
  else
    echo -e "${RED}[!] Nginx configuration test failed${NC}"
    echo -e "${YELLOW}[i] Please check the configuration manually${NC}"
  fi
else
  echo -e "${RED}[!] sudo not available. Manual Nginx setup required.${NC}"
  echo -e "${YELLOW}[i] Configuration saved as: ${SERVICE_NAME}_nginx.conf${NC}"
  echo -e "${YELLOW}[i] Manual steps:${NC}"
  echo -e "${YELLOW}  1. sudo mv ${SERVICE_NAME}_nginx.conf $NGINX_CONF${NC}"
  echo -e "${YELLOW}  2. sudo ln -sf $NGINX_CONF $NGINX_ENABLED${NC}"
  echo -e "${YELLOW}  3. sudo nginx -t${NC}"
  echo -e "${YELLOW}  4. sudo systemctl restart nginx${NC}"
fi

# 7. Final setup summary
echo -e "\n${GREEN}==============================="
echo -e " Production Setup Complete!"
echo -e "===============================${NC}"
echo -e "${GREEN}[✓] Application built and deployed${NC}"
echo -e "${GREEN}[✓] Data directory configured: $DATA_DIR${NC}"
echo -e "${GREEN}[✓] Environment file created: $ENV_FILE${NC}"
echo -e "${GREEN}[✓] Systemd service installed and running${NC}"
echo -e "${GREEN}[✓] Nginx reverse proxy configured and running${NC}"

echo -e "\n${BLUE}Access Points:${NC}"
echo -e "${YELLOW}• Primary: http://localhost (port 80 via Nginx)${NC}"
echo -e "${YELLOW}• Primary: http://your_server_ip (port 80 via Nginx)${NC}"
echo -e "${YELLOW}• Direct: http://localhost:3001 (direct backend access)${NC}"
echo -e "${YELLOW}• API: http://localhost/api/ (via Nginx proxy)${NC}"
echo -e "${YELLOW}• Health: http://localhost/health (health check endpoint)${NC}"

echo -e "\n${BLUE}Next Steps:${NC}"
echo -e "${YELLOW}1. Edit $ENV_FILE to add your OpenAI API key (optional)${NC}"
echo -e "${YELLOW}2. Access the application at http://localhost${NC}"
echo -e "${YELLOW}3. Upload your bank CSV files to test multi-month processing${NC}"
echo -e "${YELLOW}4. Test internal transfer detection with sample data${NC}"

echo -e "\n${BLUE}Service Management:${NC}"
echo -e "${YELLOW}• FocusBudget: sudo systemctl [start|stop|restart|status] ${SERVICE_NAME}${NC}"
echo -e "${YELLOW}• Nginx: sudo systemctl [start|stop|restart|status] nginx${NC}"
echo -e "${YELLOW}• FocusBudget logs: sudo journalctl -u ${SERVICE_NAME} -f${NC}"
echo -e "${YELLOW}• Nginx logs: sudo journalctl -u nginx -f${NC}"
echo -e "${YELLOW}• Nginx access logs: sudo tail -f /var/log/nginx/access.log${NC}"
echo -e "${YELLOW}• Nginx error logs: sudo tail -f /var/log/nginx/error.log${NC}"

echo -e "\n${BLUE}Configuration Files:${NC}"
echo -e "${YELLOW}• FocusBudget service: /etc/systemd/system/${SERVICE_NAME}.service${NC}"
echo -e "${YELLOW}• Nginx config: /etc/nginx/sites-available/${SERVICE_NAME}${NC}"
echo -e "${YELLOW}• Environment: $ENV_FILE${NC}"
echo -e "${YELLOW}• Data directory: $DATA_DIR${NC}"

echo -e "\n${GREEN}FocusBudget is ready for production use with Nginx reverse proxy!${NC}"
