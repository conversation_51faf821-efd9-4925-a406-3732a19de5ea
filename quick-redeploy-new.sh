#!/bin/bash

# Quick Redeployment Script for FocusBudget
# This script provides quick options for common deployment tasks

# Colors for better readability
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

# Configuration
REMOTE_USER="labmaster"
REMOTE_HOST="**********"
REMOTE_PATH="/home/<USER>/focusbudget"

echo -e "\n${BLUE}==============================="
echo -e " FocusBudget Quick Operations"
echo -e "===============================${NC}\n"

echo -e "${YELLOW}Select an operation:${NC}"
echo -e "1) Full redeploy (package + transfer + build)"
echo -e "2) Restart remote services"
echo -e "3) Check remote status"
echo -e "4) View remote logs"
echo -e "5) Test remote deployment"
echo -e "6) SSH to remote server"
echo -e "7) Exit"

read -p "Enter your choice [1-7]: " choice

case $choice in
  1)
    echo -e "\n${BLUE}[>] Starting full redeployment${NC}"
    ./remote-deploy.sh
    ;;
  2)
    echo -e "\n${BLUE}[>] Restarting remote services${NC}"
    echo -e "${YELLOW}[i] You may be prompted for password${NC}"
    ssh "${REMOTE_USER}@${REMOTE_HOST}" "sudo systemctl restart focusbudget nginx"
    echo -e "${GREEN}[✓] Services restarted${NC}"
    echo -e "${YELLOW}[i] Check status: http://${REMOTE_HOST}${NC}"
    ;;
  3)
    echo -e "\n${BLUE}[>] Checking remote status${NC}"
    echo -e "${YELLOW}[i] You may be prompted for password${NC}"
    ssh "${REMOTE_USER}@${REMOTE_HOST}" "
      echo 'FocusBudget Service:'
      sudo systemctl status focusbudget --no-pager -l
      echo ''
      echo 'Nginx Service:'
      sudo systemctl status nginx --no-pager -l
      echo ''
      echo 'Port Status:'
      ss -tuln | grep -E ':(80|3001)'
    "
    ;;
  4)
    echo -e "\n${BLUE}[>] Viewing remote logs (press Ctrl+C to exit)${NC}"
    echo -e "${YELLOW}Choose log type:${NC}"
    echo -e "1) FocusBudget logs"
    echo -e "2) Nginx logs"
    echo -e "3) Both (combined)"
    read -p "Enter choice [1-3]: " log_choice
    
    echo -e "${YELLOW}[i] You may be prompted for password${NC}"
    case $log_choice in
      1)
        ssh "${REMOTE_USER}@${REMOTE_HOST}" "sudo journalctl -u focusbudget -f"
        ;;
      2)
        ssh "${REMOTE_USER}@${REMOTE_HOST}" "sudo journalctl -u nginx -f"
        ;;
      3)
        ssh "${REMOTE_USER}@${REMOTE_HOST}" "sudo journalctl -u focusbudget -u nginx -f"
        ;;
      *)
        echo -e "${RED}[!] Invalid choice${NC}"
        ;;
    esac
    ;;
  5)
    echo -e "\n${BLUE}[>] Testing remote deployment${NC}"
    echo -e "${YELLOW}[i] You may be prompted for password${NC}"
    ssh "${REMOTE_USER}@${REMOTE_HOST}" "cd '${REMOTE_PATH}/focusbudget' && ./test-deployment.sh"
    ;;
  6)
    echo -e "\n${BLUE}[>] Connecting to remote server${NC}"
    echo -e "${YELLOW}[i] You will be connected to ${REMOTE_USER}@${REMOTE_HOST}${NC}"
    echo -e "${YELLOW}[i] Project location: ${REMOTE_PATH}/focusbudget${NC}"
    echo -e "${YELLOW}[i] You may be prompted for password${NC}"
    ssh "${REMOTE_USER}@${REMOTE_HOST}"
    ;;
  7)
    echo -e "\n${YELLOW}[i] Goodbye!${NC}"
    exit 0
    ;;
  *)
    echo -e "\n${RED}[!] Invalid choice. Please select 1-7.${NC}"
    exit 1
    ;;
esac

echo -e "\n${GREEN}[✓] Operation completed${NC}"
