<svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Hexagonal container -->
  <path d="M50 25 L150 25 L175 75 L175 125 L150 175 L50 175 L25 125 L25 75 Z" 
        fill="url(#logoGradient)" 
        stroke="none"/>
  
  <!-- Inner white background -->
  <path d="M60 40 L140 40 L160 80 L160 120 L140 160 L60 160 L40 120 L40 80 Z" 
        fill="white" 
        stroke="none"/>
  
  <!-- Dollar sign -->
  <g transform="translate(100, 100)">
    <!-- Vertical line through dollar sign -->
    <rect x="-3" y="-45" width="6" height="90" fill="url(#logoGradient)" rx="3"/>
    
    <!-- Top curve of S -->
    <path d="M -25 -25 Q -25 -35 -15 -35 L 15 -35 Q 25 -35 25 -25 Q 25 -15 15 -15 L -5 -15" 
          fill="none" 
          stroke="url(#logoGradient)" 
          stroke-width="8" 
          stroke-linecap="round"/>
    
    <!-- Bottom curve of S -->
    <path d="M 25 25 Q 25 35 15 35 L -15 35 Q -25 35 -25 25 Q -25 15 -15 15 L 5 15" 
          fill="none" 
          stroke="url(#logoGradient)" 
          stroke-width="8" 
          stroke-linecap="round"/>
    
    <!-- Middle connecting line -->
    <path d="M -5 -15 L 5 15" 
          fill="none" 
          stroke="url(#logoGradient)" 
          stroke-width="8" 
          stroke-linecap="round"/>
  </g>
</svg>
