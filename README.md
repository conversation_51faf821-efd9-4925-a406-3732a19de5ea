# BudgetPage - Personal Budget Management Application

A comprehensive personal budget management application designed for home servers and cloud appliances. Upload bank CSV data, automatically categorize transactions with AI, track spending across multiple months, and manage your finances with intelligent transfer detection.

## 🌟 Key Features

### 📊 **Multi-Month Data Processing**
- **Smart CSV Import**: Automatically organizes transactions by their actual dates across multiple months
- **Historical Organization**: Creates separate monthly records for each month represented in your bank data
- **Date Preservation**: Maintains original transaction dates exactly as they appear in CSV files
- **Bulk Processing**: Handle 3+ months of bank data in a single upload

### 🔄 **Internal Transfer Detection**
- **Automatic Detection**: Identifies transfers between accounts using multiple criteria:
  - Same date and amount (opposite signs)
  - Transfer-related keywords in descriptions
  - Account type indicators (savings, checking, credit card)
- **Confidence Scoring**: Uses intelligent algorithms to determine transfer likelihood
- **Clean Reporting**: Excludes internal transfers from spending calculations to prevent double-counting

### 🤖 **AI-Powered Categorization**
- **OpenAI Integration**: Automatically categorizes transactions using GPT models
- **Smart Learning**: Learns from your keyword mappings and categorization patterns
- **Multi-Month Awareness**: AI understands that data may span multiple months
- **Transfer Recognition**: Automatically identifies and categorizes internal transfers

### 🏠 **Server-First Architecture**
- **Centralized Storage**: All data stored on server for cross-device access
- **Instant Sync**: Upload on one device, access immediately on another
- **Session Management**: Only UI preferences (dark mode) stored in browser
- **Data Persistence**: Transactions, budgets, settings, and license stored server-side

### 📱 **Cross-Device Compatibility**
- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile
- **Real-Time Updates**: Changes reflect immediately across all connected devices
- **Session Continuity**: Maintain your workflow across different devices

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd BudgetPage/project
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment (optional)**
   ```bash
   cp .env.example .env
   # Edit .env to set DATA_DIR and OPENAI_API_KEY
   ```

4. **Start the development server**
   ```bash
   npm run dev
   ```

5. **Start the backend server**
   ```bash
   node server.cjs
   ```

6. **Access the application**
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:3001

### Production Deployment

#### Automated Deployment (Recommended)

1. **Run the deployment script**
   ```bash
   ./build-and-deploy.sh
   ```

   This script will:
   - Install dependencies
   - Build the application for production
   - Set up data directory with proper permissions
   - Create environment configuration
   - Install and configure systemd service
   - Optionally set up Nginx reverse proxy

2. **Test the deployment**
   ```bash
   ./test-deployment.sh
   ```

#### Manual Deployment

1. **Build the application**
   ```bash
   npm install
   npm run build
   ```

2. **Set up environment**
   ```bash
   mkdir -p data logs
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start the production server**
   ```bash
   node server.cjs
   ```

#### Service Management

Once deployed with the script, manage the service using systemctl:

```bash
# Check status
sudo systemctl status budgetpage

# Start/stop/restart
sudo systemctl start budgetpage
sudo systemctl stop budgetpage
sudo systemctl restart budgetpage

# View logs
sudo journalctl -u budgetpage -f
```

## 📁 Data Storage

### Server Storage Structure
```
data/
├── transactions.json      # All transaction data
├── budget.json           # Budget categories and limits
├── history.json          # Monthly history summaries
├── keywords.json         # Keyword mapping rules
├── tracked_expenses.json # Expense tracking data
├── license.json          # License information
└── settings.json         # API keys and AI model settings
```

### Browser Storage (Session Only)
- **Dark Mode Preference**: `budget_session_dark_mode`
- **Current Tab**: `budget_session_current_tab`

## 🔧 Configuration

### Environment Variables
- `DATA_DIR`: Directory for data storage (default: `./data`)
- `OPENAI_API_KEY`: OpenAI API key for transaction categorization
- `PORT`: Server port (default: 3001)

### AI Model Configuration
- **Default Model**: `gpt-3.5-turbo-instruct`
- **Configurable**: Change AI model in Settings tab
- **API Key**: Set in Settings tab or environment variable

## 📊 CSV Import Format

The application supports standard bank CSV formats with the following expected columns:
- **Date**: Transaction date (supports DD/MM/YYYY, DD-MM-YYYY, and YYYY-MM-DD formats)
- **Description**: Transaction description
- **Amount**: Transaction amount (negative for expenses, positive for income)
- **Account** (optional): Account identifier for transfer detection

### Supported Date Formats
- **DD/MM/YYYY**: `15/10/2024` (European format)
- **DD-MM-YYYY**: `15-10-2024` (European format with dashes)
- **YYYY-MM-DD**: `2024-10-15` (ISO format)

All dates are automatically converted to YYYY-MM-DD format for internal storage and processing.

### Example CSV Format
```csv
Date,Description,Amount,Account
15/10/2024,Grocery Store Purchase,-85.50,Checking
15/10/2024,Transfer to Savings,-500.00,Checking
15/10/2024,Transfer from Checking,500.00,Savings
05/11/2024,Electric Bill,-120.00,Checking
01/12/2024,Salary Deposit,3500.00,Checking
```

## 🔄 Internal Transfer Detection

### Detection Criteria
1. **Same Date**: Transactions must occur on the same date
2. **Opposite Amounts**: One positive, one negative with equal absolute values
3. **Transfer Keywords**: Descriptions containing transfer-related terms
4. **Account Indicators**: References to different account types

### Confidence Factors
- **Base Confidence**: 50% for same date and amount
- **Keyword Boost**: +10% for each transfer-related keyword
- **Similarity Boost**: +20% for similar descriptions
- **Account Boost**: +30% for cross-account references

### Threshold
- **Minimum Confidence**: 70% required for transfer classification

## 🎯 Monthly Organization

### Automatic Processing
- Transactions organized by actual transaction dates
- Separate monthly records created automatically
- Historical data properly categorized by month
- Spending and income totals calculated per month

### Monthly History Structure
```json
{
  "month": "October",
  "year": 2024,
  "transactions": [...],
  "totalSpent": 1250.50,
  "totalIncome": 3500.00,
  "netIncome": 2249.50,
  "categories": {...},
  "incomeCategories": {...}
}
```

## 🛠️ API Endpoints

### Storage Endpoints
- `GET /api/storage/:collection` - Retrieve data
- `POST /api/storage/:collection` - Save data
- `GET /api/storage/status` - Check storage availability
- `POST /api/storage/clear` - Clear all data

### Configuration Endpoints
- `GET /api/config/openai` - Get OpenAI API key
- `GET /api/status` - Server status and statistics

## 🔒 Security & Privacy

### Data Protection
- **Local Storage**: All data stored on your server
- **No Cloud Dependencies**: Works completely offline (except AI features)
- **API Key Security**: OpenAI keys stored server-side only
- **Session Isolation**: Each device maintains independent session state

### File Security
- **Size Limits**: 5MB maximum file size for uploads
- **JSON Validation**: Robust error handling for corrupted data
- **Access Control**: File system permissions respected

## 🧪 Testing

### Manual Testing
1. **Upload Multi-Month CSV**: Test with 3+ months of bank data
2. **Transfer Detection**: Include internal transfers in test data
3. **Cross-Device Access**: Verify data appears on multiple devices
4. **AI Categorization**: Test with OpenAI API key configured

### Expected Results
- Transactions organized into correct monthly buckets
- Internal transfers detected and excluded from spending
- Data immediately available across all devices
- AI categorization working with preserved dates

## 📝 Change History

### Version 2.0.0 - Multi-Month & Server-First Architecture (2025-07-13)

#### 🔄 **Major Changes**
- **Server-First Storage**: Complete migration from browser-first to server-first data storage
- **Multi-Month Processing**: Enhanced transaction processing to handle CSV data spanning multiple months
- **Internal Transfer Detection**: Intelligent detection and handling of transfers between accounts
- **Cross-Device Synchronization**: Real-time data sharing across multiple devices

#### 🆕 **New Features**
- **TransactionProcessor Class**: Comprehensive transaction processing engine
- **Transfer Detection Algorithm**: Multi-criteria transfer identification system
- **Monthly Organization**: Automatic organization of transactions by actual dates
- **Enhanced User Feedback**: Detailed processing results and statistics
- **Session-Only Browser Storage**: UI preferences stored locally, data stored server-side

#### 🔧 **Technical Improvements**
- **Migration System**: Automatic migration from browser storage to server storage
- **Duplicate Detection**: Enhanced duplicate transaction detection
- **Error Handling**: Robust fallback mechanisms for storage operations
- **API Enhancement**: Improved OpenAI integration with multi-month awareness
- **Date Format Support**: Comprehensive support for DD/MM/YYYY, DD-MM-YYYY, and YYYY-MM-DD formats
- **Date Normalization**: Automatic conversion of all date formats to YYYY-MM-DD for consistent processing

#### 📁 **File Changes**
- **Added**: `src/utils/transactionProcessor.ts` - Enhanced transaction processing
- **Modified**: `src/utils/storage.ts` - Server-first storage implementation
- **Modified**: `src/tabs/SpendingTab.tsx` - Enhanced processing feedback
- **Modified**: `src/tabs/DataTab.tsx` - Improved CSV import with DD/MM/YYYY support
- **Modified**: `src/utils/openai.ts` - Enhanced AI prompts for date format handling
- **Modified**: `src/App.tsx` - Session-based dark mode handling
- **Modified**: `server.js` → `server.cjs` - CommonJS compatibility
- **Modified**: `src/utils/errorLogger.js` → `src/utils/errorLogger.cjs` - Module compatibility
- **Enhanced**: `build-and-deploy.sh` - Complete production deployment automation
- **Added**: `test-deployment.sh` - Deployment validation and testing script

#### 🐛 **Bug Fixes**
- **Date Preservation**: Transaction dates now preserved exactly as in CSV
- **Date Format Handling**: Proper support for DD/MM/YYYY format (European date format)
- **Transfer Double-Counting**: Internal transfers excluded from spending calculations
- **Cross-Device Data Loss**: All data now persists server-side
- **Monthly Allocation**: Transactions properly allocated to correct months

#### 🎯 **User Experience Improvements**
- **Processing Feedback**: Detailed results showing transfers detected and months processed
- **Data Persistence**: Immediate availability of data across all devices
- **Transfer Transparency**: Clear indication of detected internal transfers
- **Historical Accuracy**: Proper monthly organization of historical data

### Version 1.0.0 - Initial Release
- Basic budget tracking functionality
- CSV import capabilities
- OpenAI transaction categorization
- Browser-based data storage
- Single-month transaction processing
