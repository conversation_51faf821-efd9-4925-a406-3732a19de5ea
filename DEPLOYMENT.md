# FocusBudget Deployment Scripts

This directory contains automated deployment scripts for FocusBudget to remote servers.

## 🚀 Quick Start

### Full Remote Deployment
```bash
./remote-deploy.sh
```

### Quick Operations
```bash
./quick-redeploy.sh
```

## 📋 Scripts Overview

### `remote-deploy.sh` - Full Remote Deployment
**Purpose**: Complete deployment pipeline from local development to remote production server.

**What it does**:
1. ✅ Packages the entire `/BudgetPage/project` directory into a timestamped zip file
2. ✅ Excludes unnecessary files (node_modules, dist, logs, etc.)
3. ✅ Transfers the package to `labmaster@**********`
4. ✅ Extracts and sets up the project on the remote server
5. ✅ Runs `build-and-deploy.sh` on the remote server
6. ✅ Tests the deployment with `test-deployment.sh`
7. ✅ Provides access URLs and management commands

**Authentication**:
- **SSH Key**: Automatically detects and uses SSH key authentication if available
- **Password**: Falls back to password authentication with user prompts
- **Multiple Prompts**: When using password auth, you'll be prompted several times during deployment

**Requirements**:
- SSH access to `labmaster@**********` (key-based or password)
- `zip` utility installed locally
- Remote server has sudo access for systemd and nginx

### `quick-redeploy.sh` - Quick Operations Menu
**Purpose**: Interactive menu for common remote operations without full redeployment.

**Options**:
1. **Full redeploy** - Runs `remote-deploy.sh`
2. **Restart services** - Restarts budgetpage and nginx services
3. **Check status** - Shows service status and port information
4. **View logs** - Real-time log viewing (BudgetPage, Nginx, or both)
5. **Test deployment** - Runs remote deployment tests
6. **SSH access** - Direct SSH connection to remote server

## 🔧 Configuration

### Remote Server Details
- **Host**: `**********`
- **User**: `labmaster`
- **Project Path**: `/home/<USER>/focusbudget`
- **Services**: `budgetpage` (port 3001), `nginx` (port 80)

### Local Project Structure
```
/BudgetPage/
├── project/                 # Main project directory (gets packaged as focusbudget)
│   ├── src/                # React source code
│   ├── build-and-deploy.sh # Remote deployment script
│   ├── test-deployment.sh  # Remote testing script
│   ├── package.json        # Dependencies
│   └── server.cjs          # Backend server
├── remote-deploy.sh        # This deployment script
└── quick-redeploy.sh       # Quick operations menu
```

## 🌐 Access Points After Deployment

| URL | Purpose |
|-----|---------|
| `http://**********` | Main application (via Nginx) |
| `http://**********:3001` | Direct backend access |
| `http://**********/api/storage/status` | API health check |

## 🛠️ Troubleshooting

### SSH Connection Issues
```bash
# Test SSH connectivity
ssh labmaster@********** exit

# If fails, check:
# 1. SSH key is added: ssh-add ~/.ssh/id_rsa
# 2. Key is authorized on remote server
# 3. Network connectivity to **********
```

### Deployment Failures
```bash
# Check remote logs
ssh labmaster@********** 'sudo journalctl -u budgetpage -f'

# Check remote service status
ssh labmaster@********** 'sudo systemctl status budgetpage nginx'

# Manual remote deployment test
ssh labmaster@********** 'cd /home/<USER>/budgetpage/budgetpage && ./test-deployment.sh'
```

### Package Creation Issues
```bash
# Check if zip is installed
which zip

# Install zip if missing:
# Ubuntu/Debian: sudo apt-get install zip
# RHEL/CentOS: sudo yum install zip
```

## 📊 Deployment Process Flow

```mermaid
graph TD
    A[Local Development] --> B[Run remote-deploy.sh]
    B --> C[Package Project Files]
    C --> D[Transfer to Remote Server]
    D --> E[Extract on Remote]
    E --> F[Run build-and-deploy.sh]
    F --> G[Install Dependencies]
    G --> H[Build Frontend]
    H --> I[Setup Data Directory]
    I --> J[Configure Environment]
    J --> K[Install Systemd Service]
    K --> L[Setup Nginx Proxy]
    L --> M[Start Services]
    M --> N[Test Deployment]
    N --> O[Deployment Complete]
```

## 🔄 Quick Operations Workflow

```mermaid
graph TD
    A[Run quick-redeploy.sh] --> B[Select Operation]
    B --> C{Operation Type}
    C -->|1| D[Full Redeploy]
    C -->|2| E[Restart Services]
    C -->|3| F[Check Status]
    C -->|4| G[View Logs]
    C -->|5| H[Test Deployment]
    C -->|6| I[SSH Access]
    D --> J[Run remote-deploy.sh]
    E --> K[systemctl restart]
    F --> L[systemctl status]
    G --> M[journalctl -f]
    H --> N[test-deployment.sh]
    I --> O[ssh connection]
```

## 📝 Example Usage

### First Time Deployment
```bash
# Deploy to remote server
./remote-deploy.sh

# Access the application
curl http://**********/api/storage/status
```

### Regular Updates
```bash
# Quick menu for common operations
./quick-redeploy.sh

# Select option 1 for full redeploy
# Or option 2 to just restart services
```

### Monitoring
```bash
# Check status
./quick-redeploy.sh
# Select option 3

# View live logs
./quick-redeploy.sh
# Select option 4, then option 3 for combined logs
```

## 🔐 Security Notes

- SSH key-based authentication required (no password prompts)
- Remote server requires sudo access for service management
- All data stored in `/home/<USER>/budgetpage/budgetpage/data/`
- Services run as `labmaster` user with systemd security restrictions
- Nginx provides reverse proxy with security headers

## 📞 Support

If deployment fails:
1. Check the error messages in the script output
2. Verify SSH connectivity and permissions
3. Check remote server logs using `quick-redeploy.sh` option 4
4. Ensure remote server has sufficient disk space and memory
