#!/bin/bash

# FocusBudget Remote Deployment Script
# This script handles SSH key setup, password authentication, and remote deployment

# Exit on error
set -e

# Colors for better readability
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

# Configuration
REMOTE_USER="labmaster"
REMOTE_HOST="**********"
REMOTE_PATH="/focusbudget"
PROJECT_NAME="focusbudget"
LOCAL_PROJECT_PATH="./project"

echo -e "\n${BLUE}==============================="
echo -e " FocusBudget Remote Deployment"
echo -e "===============================${NC}\n"

# Function to check if SSH key exists and is loaded
check_ssh_key() {
    local ssh_key_path="$HOME/.ssh/id_rsa"
    local ssh_pub_key_path="$HOME/.ssh/id_rsa.pub"
    
    if [ ! -f "$ssh_key_path" ]; then
        echo -e "${YELLOW}[i] SSH key not found at $ssh_key_path${NC}"
        return 1
    fi
    
    if [ ! -f "$ssh_pub_key_path" ]; then
        echo -e "${YELLOW}[i] SSH public key not found at $ssh_pub_key_path${NC}"
        return 1
    fi
    
    # Check if key is loaded in ssh-agent
    if ! ssh-add -l >/dev/null 2>&1; then
        echo -e "${YELLOW}[i] SSH agent not running or no keys loaded${NC}"
        return 1
    fi
    
    return 0
}

# Function to generate SSH key if it doesn't exist
generate_ssh_key() {
    local ssh_key_path="$HOME/.ssh/id_rsa"
    
    echo -e "${BLUE}[>] Generating SSH key pair${NC}"
    
    # Create .ssh directory if it doesn't exist
    mkdir -p "$HOME/.ssh"
    chmod 700 "$HOME/.ssh"
    
    # Generate SSH key
    ssh-keygen -t rsa -b 4096 -f "$ssh_key_path" -N "" -C "$(whoami)@$(hostname)-focusbudget"
    
    echo -e "${GREEN}[✓] SSH key generated at $ssh_key_path${NC}"
}

# Function to setup SSH key authentication (interactive)
setup_ssh_key() {
    local ssh_pub_key_path="$HOME/.ssh/id_rsa.pub"

    if [ ! -f "$ssh_pub_key_path" ]; then
        echo -e "${RED}[!] SSH public key not found${NC}"
        return 1
    fi

    echo -e "${BLUE}[>] Setting up SSH key authentication${NC}"
    echo -e "${YELLOW}[i] You will be prompted for the remote server password${NC}"

    # Copy SSH key to remote server
    if ssh-copy-id -i "$ssh_pub_key_path" "${REMOTE_USER}@${REMOTE_HOST}"; then
        echo -e "${GREEN}[✓] SSH key copied to remote server${NC}"
        return 0
    else
        echo -e "${RED}[!] Failed to copy SSH key to remote server${NC}"
        return 1
    fi
}

# Function to setup SSH key using existing password (non-interactive)
setup_ssh_key_with_password() {
    local ssh_pub_key_path="$HOME/.ssh/id_rsa.pub"
    local password="$1"

    if [ ! -f "$ssh_pub_key_path" ]; then
        echo -e "${RED}[!] SSH public key not found at $ssh_pub_key_path${NC}"
        return 1
    fi

    if [ -z "$password" ]; then
        echo -e "${RED}[!] Password required for SSH key setup${NC}"
        return 1
    fi

    echo -e "${YELLOW}[i] Attempting to copy SSH key using sshpass...${NC}"

    # Copy SSH key to remote server using sshpass with more verbose output
    if sshpass -p "$password" ssh-copy-id -o StrictHostKeyChecking=no -i "$ssh_pub_key_path" "${REMOTE_USER}@${REMOTE_HOST}"; then
        echo -e "${GREEN}[✓] SSH key copy command completed${NC}"
        return 0
    else
        echo -e "${RED}[!] SSH key copy command failed${NC}"
        return 1
    fi
}

# Function to test SSH connection
test_ssh_connection() {
    echo -e "${BLUE}[>] Testing SSH connection to ${REMOTE_USER}@${REMOTE_HOST}${NC}"

    # Try SSH connection with key authentication only (no password fallback)
    if ssh -o ConnectTimeout=10 -o BatchMode=yes -o PasswordAuthentication=no -o StrictHostKeyChecking=no "${REMOTE_USER}@${REMOTE_HOST}" exit 2>/dev/null; then
        echo -e "${GREEN}[✓] SSH key authentication working${NC}"
        return 0
    else
        echo -e "${YELLOW}[i] SSH key authentication not working${NC}"
        return 1
    fi
}

# Function to verify SSH key was properly installed
verify_ssh_key_installation() {
    echo -e "${BLUE}[>] Verifying SSH key installation${NC}"

    # Test with a simple command
    if ssh -o ConnectTimeout=10 -o BatchMode=yes -o PasswordAuthentication=no -o StrictHostKeyChecking=no "${REMOTE_USER}@${REMOTE_HOST}" "echo 'SSH key test successful'" 2>/dev/null; then
        echo -e "${GREEN}[✓] SSH key verification successful${NC}"
        return 0
    else
        echo -e "${RED}[!] SSH key verification failed${NC}"
        return 1
    fi
}

# Function to get and store remote password
get_remote_password() {
    echo -e "${YELLOW}[i] SSH key authentication not available${NC}"
    echo -e "${YELLOW}[i] Password authentication will be used${NC}"
    echo -e "${YELLOW}[i] You may be prompted for passwords multiple times during deployment${NC}"

    read -s -p "Enter password for ${REMOTE_USER}@${REMOTE_HOST}: " REMOTE_PASSWORD
    echo

    if [ -z "$REMOTE_PASSWORD" ]; then
        echo -e "${RED}[!] Password cannot be empty${NC}"
        exit 1
    fi

    # Test the password
    echo -e "${BLUE}[>] Testing password authentication${NC}"
    if sshpass -p "$REMOTE_PASSWORD" ssh -o ConnectTimeout=10 "${REMOTE_USER}@${REMOTE_HOST}" exit 2>/dev/null; then
        echo -e "${GREEN}[✓] Password authentication working${NC}"
        export SSHPASS="$REMOTE_PASSWORD"

        # Offer to set up SSH keys for future passwordless access
        echo -e "\n${YELLOW}[i] Password authentication successful!${NC}"
        echo -e "${YELLOW}[i] Would you like to set up SSH key authentication for future passwordless deployments?${NC}"
        read -p "Set up SSH keys now? [Y/n]: " setup_keys
        setup_keys=${setup_keys:-Y}

        if [[ "$setup_keys" =~ ^[Yy]$ ]]; then
            echo -e "${BLUE}[>] Setting up SSH keys for future use${NC}"

            # Generate SSH key if it doesn't exist
            if [ ! -f "$HOME/.ssh/id_rsa" ]; then
                echo -e "${BLUE}[>] Generating new SSH key pair${NC}"
                generate_ssh_key
            else
                echo -e "${GREEN}[✓] SSH key already exists at $HOME/.ssh/id_rsa${NC}"
            fi

            # Ensure SSH key has correct permissions
            chmod 600 "$HOME/.ssh/id_rsa" 2>/dev/null || true
            chmod 644 "$HOME/.ssh/id_rsa.pub" 2>/dev/null || true

            # Start ssh-agent if not running
            if ! ssh-add -l >/dev/null 2>&1; then
                echo -e "${BLUE}[>] Starting SSH agent${NC}"
                eval "$(ssh-agent -s)"
                ssh-add "$HOME/.ssh/id_rsa" 2>/dev/null || true
            else
                echo -e "${GREEN}[✓] SSH agent is running${NC}"
            fi

            # Copy SSH key using the password we already have
            echo -e "${BLUE}[>] Copying SSH key to remote server${NC}"
            echo -e "${YELLOW}[i] Using password: ${REMOTE_PASSWORD:0:3}***${NC}"

            if setup_ssh_key_with_password "$REMOTE_PASSWORD"; then
                echo -e "${GREEN}[✓] SSH key copy completed${NC}"

                # Wait a moment for the key to be processed
                sleep 2

                # Test the SSH key
                echo -e "${BLUE}[>] Verifying SSH key authentication${NC}"
                if verify_ssh_key_installation; then
                    echo -e "${GREEN}[✓] SSH key authentication verified and working!${NC}"
                    echo -e "${GREEN}[✓] Future deployments will be passwordless${NC}"
                    USE_SSH_KEY=true
                    unset SSHPASS  # Clear password since we now have key auth
                    return 0
                else
                    echo -e "${YELLOW}[i] SSH key was copied but authentication test failed${NC}"
                    echo -e "${YELLOW}[i] This might be due to server configuration or timing${NC}"
                    echo -e "${YELLOW}[i] Let's try a manual verification...${NC}"

                    # Try one more time with a longer timeout
                    sleep 3
                    if verify_ssh_key_installation; then
                        echo -e "${GREEN}[✓] SSH key authentication working after retry!${NC}"
                        USE_SSH_KEY=true
                        unset SSHPASS
                        return 0
                    else
                        echo -e "${YELLOW}[i] Continuing with password authentication for this deployment${NC}"
                        echo -e "${YELLOW}[i] Try running the script again to test if SSH keys work${NC}"
                    fi
                fi
            else
                echo -e "${RED}[!] SSH key setup failed${NC}"
                echo -e "${YELLOW}[i] Continuing with password authentication${NC}"
            fi
        else
            echo -e "${YELLOW}[i] Skipping SSH key setup, continuing with password authentication${NC}"
        fi

        return 0
    else
        echo -e "${RED}[!] Password authentication failed${NC}"
        exit 1
    fi
}

# Check if sshpass is installed for password authentication
check_sshpass() {
    if ! command -v sshpass >/dev/null 2>&1; then
        echo -e "${YELLOW}[i] sshpass not found, installing...${NC}"
        
        if command -v apt-get >/dev/null 2>&1; then
            sudo apt-get update && sudo apt-get install -y sshpass
        elif command -v yum >/dev/null 2>&1; then
            sudo yum install -y sshpass
        elif command -v dnf >/dev/null 2>&1; then
            sudo dnf install -y sshpass
        elif command -v pacman >/dev/null 2>&1; then
            sudo pacman -S --noconfirm sshpass
        else
            echo -e "${RED}[!] Could not install sshpass. Please install it manually.${NC}"
            exit 1
        fi
        
        echo -e "${GREEN}[✓] sshpass installed${NC}"
    fi
}

# Main SSH setup function
setup_ssh_authentication() {
    echo -e "${BLUE}[>] Setting up SSH authentication${NC}"

    # Check if SSH key exists and works
    if check_ssh_key && test_ssh_connection; then
        echo -e "${GREEN}[✓] SSH key authentication already configured${NC}"
        USE_SSH_KEY=true
        return 0
    fi

    # Check if we can connect with password first (more common scenario)
    echo -e "${BLUE}[>] Testing connection to ${REMOTE_USER}@${REMOTE_HOST}${NC}"

    # Try SSH key first if it exists
    if [ -f "$HOME/.ssh/id_rsa.pub" ]; then
        echo -e "${YELLOW}[i] Found existing SSH key at $HOME/.ssh/id_rsa.pub${NC}"
        echo -e "${BLUE}[>] Testing existing SSH key authentication...${NC}"
        if test_ssh_connection; then
            echo -e "${GREEN}[✓] SSH key authentication working${NC}"
            USE_SSH_KEY=true
            return 0
        else
            echo -e "${YELLOW}[i] SSH key authentication not working - key may not be authorized on remote server${NC}"
        fi
    else
        echo -e "${YELLOW}[i] No SSH key found at $HOME/.ssh/id_rsa.pub${NC}"
    fi

    # Try password authentication
    echo -e "${YELLOW}[i] Attempting password authentication${NC}"
    check_sshpass

    # Get password and test connection (this will offer SSH key setup if successful)
    echo -e "${BLUE}[>] Getting password and testing connection...${NC}"
    get_remote_password

    # Check what get_remote_password decided
    echo -e "${BLUE}[>] Authentication setup result: USE_SSH_KEY=$USE_SSH_KEY${NC}"

    # If get_remote_password set USE_SSH_KEY=true, we're done
    if [ "$USE_SSH_KEY" = true ]; then
        echo -e "${GREEN}[✓] SSH key authentication configured and verified${NC}"
        return 0
    fi

    # Otherwise, we're using password authentication
    USE_SSH_KEY=false
    echo -e "${YELLOW}[i] Using password authentication for this deployment${NC}"
}

# Function to execute remote commands
remote_exec() {
    local command="$1"
    local description="$2"
    
    if [ -n "$description" ]; then
        echo -e "${BLUE}[>] $description${NC}"
    fi
    
    if [ "$USE_SSH_KEY" = true ]; then
        ssh "${REMOTE_USER}@${REMOTE_HOST}" "$command"
    else
        sshpass -e ssh "${REMOTE_USER}@${REMOTE_HOST}" "$command"
    fi
}

# Function to copy files to remote
remote_copy() {
    local local_file="$1"
    local remote_path="$2"
    local description="$3"
    
    if [ -n "$description" ]; then
        echo -e "${BLUE}[>] $description${NC}"
    fi
    
    if [ "$USE_SSH_KEY" = true ]; then
        scp "$local_file" "${REMOTE_USER}@${REMOTE_HOST}:$remote_path"
    else
        sshpass -e scp "$local_file" "${REMOTE_USER}@${REMOTE_HOST}:$remote_path"
    fi
}

# Check prerequisites
echo -e "${BLUE}[>] Checking prerequisites${NC}"

# Check if project directory exists
if [ ! -d "$LOCAL_PROJECT_PATH" ]; then
    echo -e "${RED}[!] Project directory not found: $LOCAL_PROJECT_PATH${NC}"
    exit 1
fi

# Check if zip is available
if ! command -v zip >/dev/null 2>&1; then
    echo -e "${YELLOW}[i] Installing zip utility${NC}"
    if command -v apt-get >/dev/null 2>&1; then
        sudo apt-get update && sudo apt-get install -y zip
    elif command -v yum >/dev/null 2>&1; then
        sudo yum install -y zip
    elif command -v dnf >/dev/null 2>&1; then
        sudo dnf install -y zip
    else
        echo -e "${RED}[!] Could not install zip. Please install it manually.${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}[✓] Prerequisites checked${NC}"

# Setup SSH authentication
setup_ssh_authentication

echo -e "${GREEN}[✓] Authentication configured${NC}"
echo -e "${YELLOW}[i] Proceeding with deployment...${NC}"

# Create deployment package
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
PACKAGE_NAME="${PROJECT_NAME}_${TIMESTAMP}.zip"

echo -e "\n${BLUE}[>] Creating deployment package${NC}"
echo -e "${YELLOW}[i] Package: $PACKAGE_NAME${NC}"

# Create temporary directory for packaging
TEMP_DIR=$(mktemp -d)
cp -r "$LOCAL_PROJECT_PATH" "$TEMP_DIR/$PROJECT_NAME"

# Remove unnecessary files from package
echo -e "${BLUE}[>] Cleaning package (removing node_modules, dist, logs, etc.)${NC}"
cd "$TEMP_DIR"
find "$PROJECT_NAME" -name "node_modules" -type d -exec rm -rf {} + 2>/dev/null || true
find "$PROJECT_NAME" -name "dist" -type d -exec rm -rf {} + 2>/dev/null || true
find "$PROJECT_NAME" -name "logs" -type d -exec rm -rf {} + 2>/dev/null || true
find "$PROJECT_NAME" -name ".git" -type d -exec rm -rf {} + 2>/dev/null || true
find "$PROJECT_NAME" -name "*.log" -type f -delete 2>/dev/null || true
find "$PROJECT_NAME" -name ".DS_Store" -type f -delete 2>/dev/null || true

# Create the zip package
zip -r "$PACKAGE_NAME" "$PROJECT_NAME" >/dev/null
mv "$PACKAGE_NAME" "$OLDPWD/"
cd "$OLDPWD"
rm -rf "$TEMP_DIR"

echo -e "${GREEN}[✓] Package created: $PACKAGE_NAME${NC}"

# Transfer package to remote server
echo -e "\n${BLUE}[>] Transferring package to remote server${NC}"
remote_copy "$PACKAGE_NAME" "/tmp/$PACKAGE_NAME" "Uploading $PACKAGE_NAME"

# Setup remote directory and extract package
echo -e "\n${BLUE}[>] Setting up remote environment${NC}"
remote_exec "
    # Create project directory with sudo (root level directory)
    sudo mkdir -p '$REMOTE_PATH'
    sudo chown $REMOTE_USER:$REMOTE_USER '$REMOTE_PATH'
    cd '$REMOTE_PATH'

    # Remove old deployment if exists
    if [ -d '$PROJECT_NAME' ]; then
        echo 'Backing up existing deployment...'
        mv '$PROJECT_NAME' '${PROJECT_NAME}_backup_\$(date +%Y%m%d_%H%M%S)' 2>/dev/null || true
    fi

    # Extract new package
    echo 'Extracting package...'
    unzip -q '/tmp/$PACKAGE_NAME'

    # Set proper ownership
    sudo chown -R $REMOTE_USER:$REMOTE_USER '$REMOTE_PATH'

    # Clean up
    rm -f '/tmp/$PACKAGE_NAME'

    echo 'Package extracted successfully'
    echo 'Current directory structure:'
    ls -la '$REMOTE_PATH'
" "Setting up remote directory and extracting package"

# Get remote sudo password if using password authentication
if [ "$USE_SSH_KEY" = false ]; then
    echo -e "\n${YELLOW}[i] The remote deployment requires sudo access${NC}"
    read -s -p "Enter sudo password for ${REMOTE_USER} on remote server: " REMOTE_SUDO_PASSWORD
    echo

    if [ -z "$REMOTE_SUDO_PASSWORD" ]; then
        echo -e "${RED}[!] Sudo password cannot be empty${NC}"
        exit 1
    fi
fi

# Run remote deployment with sudo password handling
echo -e "\n${BLUE}[>] Running remote deployment${NC}"
echo -e "${YELLOW}[i] This will install Node.js, build the app, and configure services${NC}"

# Create a deployment script that handles sudo password
DEPLOY_SCRIPT="deploy_with_sudo.sh"
cat > "$DEPLOY_SCRIPT" << 'EOF'
#!/bin/bash
set -e

# Colors
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m"

PROJECT_NAME="focusbudget"
cd "/focusbudget/$PROJECT_NAME"

echo -e "${BLUE}[>] Starting remote deployment${NC}"

# Make build script executable
chmod +x build-and-deploy.sh

# Check if sudo password is provided via environment
if [ -n "$SUDO_PASSWORD" ]; then
    echo -e "${YELLOW}[i] Using provided sudo password${NC}"
    # Run deployment with sudo password from stdin
    echo "$SUDO_PASSWORD" | sudo -S ./build-and-deploy.sh
else
    echo -e "${YELLOW}[i] Running deployment (you may be prompted for sudo password)${NC}"
    ./build-and-deploy.sh
fi

echo -e "${GREEN}[✓] Remote deployment completed${NC}"
EOF

# Transfer deployment script
remote_copy "$DEPLOY_SCRIPT" "/tmp/$DEPLOY_SCRIPT" "Uploading deployment script"

# Execute remote deployment
if [ "$USE_SSH_KEY" = true ]; then
    remote_exec "
        chmod +x '/tmp/$DEPLOY_SCRIPT'
        cd '$REMOTE_PATH/$PROJECT_NAME'
        '/tmp/$DEPLOY_SCRIPT'
        rm -f '/tmp/$DEPLOY_SCRIPT'
    " "Executing remote deployment"
else
    remote_exec "
        chmod +x '/tmp/$DEPLOY_SCRIPT'
        cd '$REMOTE_PATH/$PROJECT_NAME'
        SUDO_PASSWORD='$REMOTE_SUDO_PASSWORD' '/tmp/$DEPLOY_SCRIPT'
        rm -f '/tmp/$DEPLOY_SCRIPT'
    " "Executing remote deployment with sudo password"
fi

# Clean up local files
rm -f "$PACKAGE_NAME" "$DEPLOY_SCRIPT"

echo -e "\n${GREEN}==============================="
echo -e " Deployment Completed!"
echo -e "===============================${NC}"
echo -e "${GREEN}[✓] FocusBudget deployed successfully${NC}"
echo -e "${YELLOW}[i] Access points:${NC}"
echo -e "${YELLOW}  • Main app: http://${REMOTE_HOST}${NC}"
echo -e "${YELLOW}  • Direct: http://${REMOTE_HOST}:3001${NC}"
echo -e "${YELLOW}  • API: http://${REMOTE_HOST}/api${NC}"
echo -e "${YELLOW}  • Health: http://${REMOTE_HOST}/health${NC}"

echo -e "\n${BLUE}Management commands:${NC}"
echo -e "${YELLOW}• Check status: ./quick-redeploy-new.sh (option 3)${NC}"
echo -e "${YELLOW}• View logs: ./quick-redeploy-new.sh (option 4)${NC}"
echo -e "${YELLOW}• Restart: ./quick-redeploy-new.sh (option 2)${NC}"

echo -e "\n${GREEN}Deployment completed successfully!${NC}"
