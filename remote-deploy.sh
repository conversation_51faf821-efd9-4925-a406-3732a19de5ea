#!/bin/bash

# FocusBudget Remote Deployment Script
# This script handles SSH key setup, password authentication, and remote deployment

# Exit on error
set -e

# Colors for better readability
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

# Configuration
REMOTE_USER="labmaster"
REMOTE_HOST="**********"
REMOTE_PATH="/home/<USER>/focusbudget"
PROJECT_NAME="focusbudget"
LOCAL_PROJECT_PATH="./project"

echo -e "\n${BLUE}==============================="
echo -e " FocusBudget Remote Deployment"
echo -e "===============================${NC}\n"

# Function to check if SSH key exists and is loaded
check_ssh_key() {
    local ssh_key_path="$HOME/.ssh/id_rsa"
    local ssh_pub_key_path="$HOME/.ssh/id_rsa.pub"
    
    if [ ! -f "$ssh_key_path" ]; then
        echo -e "${YELLOW}[i] SSH key not found at $ssh_key_path${NC}"
        return 1
    fi
    
    if [ ! -f "$ssh_pub_key_path" ]; then
        echo -e "${YELLOW}[i] SSH public key not found at $ssh_pub_key_path${NC}"
        return 1
    fi
    
    # Check if key is loaded in ssh-agent
    if ! ssh-add -l >/dev/null 2>&1; then
        echo -e "${YELLOW}[i] SSH agent not running or no keys loaded${NC}"
        return 1
    fi
    
    return 0
}

# Function to generate SSH key if it doesn't exist
generate_ssh_key() {
    local ssh_key_path="$HOME/.ssh/id_rsa"
    
    echo -e "${BLUE}[>] Generating SSH key pair${NC}"
    
    # Create .ssh directory if it doesn't exist
    mkdir -p "$HOME/.ssh"
    chmod 700 "$HOME/.ssh"
    
    # Generate SSH key
    ssh-keygen -t rsa -b 4096 -f "$ssh_key_path" -N "" -C "$(whoami)@$(hostname)-focusbudget"
    
    echo -e "${GREEN}[✓] SSH key generated at $ssh_key_path${NC}"
}

# Function to setup SSH key authentication
setup_ssh_key() {
    local ssh_pub_key_path="$HOME/.ssh/id_rsa.pub"
    
    if [ ! -f "$ssh_pub_key_path" ]; then
        echo -e "${RED}[!] SSH public key not found${NC}"
        return 1
    fi
    
    echo -e "${BLUE}[>] Setting up SSH key authentication${NC}"
    echo -e "${YELLOW}[i] You will be prompted for the remote server password${NC}"
    
    # Copy SSH key to remote server
    if ssh-copy-id -i "$ssh_pub_key_path" "${REMOTE_USER}@${REMOTE_HOST}"; then
        echo -e "${GREEN}[✓] SSH key copied to remote server${NC}"
        return 0
    else
        echo -e "${RED}[!] Failed to copy SSH key to remote server${NC}"
        return 1
    fi
}

# Function to test SSH connection
test_ssh_connection() {
    echo -e "${BLUE}[>] Testing SSH connection${NC}"
    
    if ssh -o ConnectTimeout=10 -o BatchMode=yes "${REMOTE_USER}@${REMOTE_HOST}" exit 2>/dev/null; then
        echo -e "${GREEN}[✓] SSH key authentication working${NC}"
        return 0
    else
        echo -e "${YELLOW}[i] SSH key authentication not working, will use password${NC}"
        return 1
    fi
}

# Function to get and store remote password
get_remote_password() {
    echo -e "${YELLOW}[i] SSH key authentication not available${NC}"
    echo -e "${YELLOW}[i] Password authentication will be used${NC}"
    echo -e "${YELLOW}[i] You may be prompted for passwords multiple times during deployment${NC}"
    
    read -s -p "Enter password for ${REMOTE_USER}@${REMOTE_HOST}: " REMOTE_PASSWORD
    echo
    
    if [ -z "$REMOTE_PASSWORD" ]; then
        echo -e "${RED}[!] Password cannot be empty${NC}"
        exit 1
    fi
    
    # Test the password
    echo -e "${BLUE}[>] Testing password authentication${NC}"
    if sshpass -p "$REMOTE_PASSWORD" ssh -o ConnectTimeout=10 "${REMOTE_USER}@${REMOTE_HOST}" exit 2>/dev/null; then
        echo -e "${GREEN}[✓] Password authentication working${NC}"
        export SSHPASS="$REMOTE_PASSWORD"
        return 0
    else
        echo -e "${RED}[!] Password authentication failed${NC}"
        exit 1
    fi
}

# Check if sshpass is installed for password authentication
check_sshpass() {
    if ! command -v sshpass >/dev/null 2>&1; then
        echo -e "${YELLOW}[i] sshpass not found, installing...${NC}"
        
        if command -v apt-get >/dev/null 2>&1; then
            sudo apt-get update && sudo apt-get install -y sshpass
        elif command -v yum >/dev/null 2>&1; then
            sudo yum install -y sshpass
        elif command -v dnf >/dev/null 2>&1; then
            sudo dnf install -y sshpass
        elif command -v pacman >/dev/null 2>&1; then
            sudo pacman -S --noconfirm sshpass
        else
            echo -e "${RED}[!] Could not install sshpass. Please install it manually.${NC}"
            exit 1
        fi
        
        echo -e "${GREEN}[✓] sshpass installed${NC}"
    fi
}

# Main SSH setup function
setup_ssh_authentication() {
    echo -e "${BLUE}[>] Setting up SSH authentication${NC}"
    
    # Check if SSH key exists and works
    if check_ssh_key && test_ssh_connection; then
        echo -e "${GREEN}[✓] SSH key authentication already configured${NC}"
        USE_SSH_KEY=true
        return 0
    fi
    
    # Ask user preference for authentication method
    echo -e "${YELLOW}Choose authentication method:${NC}"
    echo -e "1) Set up SSH key authentication (recommended)"
    echo -e "2) Use password authentication"
    read -p "Enter your choice [1-2]: " auth_choice
    
    case $auth_choice in
        1)
            # SSH key setup
            if [ ! -f "$HOME/.ssh/id_rsa" ]; then
                generate_ssh_key
            fi
            
            # Start ssh-agent if not running
            if ! ssh-add -l >/dev/null 2>&1; then
                eval "$(ssh-agent -s)"
                ssh-add "$HOME/.ssh/id_rsa"
            fi
            
            if setup_ssh_key && test_ssh_connection; then
                USE_SSH_KEY=true
                echo -e "${GREEN}[✓] SSH key authentication configured successfully${NC}"
            else
                echo -e "${YELLOW}[i] SSH key setup failed, falling back to password authentication${NC}"
                check_sshpass
                get_remote_password
                USE_SSH_KEY=false
            fi
            ;;
        2)
            # Password authentication
            check_sshpass
            get_remote_password
            USE_SSH_KEY=false
            ;;
        *)
            echo -e "${RED}[!] Invalid choice${NC}"
            exit 1
            ;;
    esac
}

# Function to execute remote commands
remote_exec() {
    local command="$1"
    local description="$2"
    
    if [ -n "$description" ]; then
        echo -e "${BLUE}[>] $description${NC}"
    fi
    
    if [ "$USE_SSH_KEY" = true ]; then
        ssh "${REMOTE_USER}@${REMOTE_HOST}" "$command"
    else
        sshpass -e ssh "${REMOTE_USER}@${REMOTE_HOST}" "$command"
    fi
}

# Function to copy files to remote
remote_copy() {
    local local_file="$1"
    local remote_path="$2"
    local description="$3"
    
    if [ -n "$description" ]; then
        echo -e "${BLUE}[>] $description${NC}"
    fi
    
    if [ "$USE_SSH_KEY" = true ]; then
        scp "$local_file" "${REMOTE_USER}@${REMOTE_HOST}:$remote_path"
    else
        sshpass -e scp "$local_file" "${REMOTE_USER}@${REMOTE_HOST}:$remote_path"
    fi
}

# Check prerequisites
echo -e "${BLUE}[>] Checking prerequisites${NC}"

# Check if project directory exists
if [ ! -d "$LOCAL_PROJECT_PATH" ]; then
    echo -e "${RED}[!] Project directory not found: $LOCAL_PROJECT_PATH${NC}"
    exit 1
fi

# Check if zip is available
if ! command -v zip >/dev/null 2>&1; then
    echo -e "${YELLOW}[i] Installing zip utility${NC}"
    if command -v apt-get >/dev/null 2>&1; then
        sudo apt-get update && sudo apt-get install -y zip
    elif command -v yum >/dev/null 2>&1; then
        sudo yum install -y zip
    elif command -v dnf >/dev/null 2>&1; then
        sudo dnf install -y zip
    else
        echo -e "${RED}[!] Could not install zip. Please install it manually.${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}[✓] Prerequisites checked${NC}"

# Setup SSH authentication
setup_ssh_authentication

echo -e "${GREEN}[✓] Authentication configured${NC}"
echo -e "${YELLOW}[i] Proceeding with deployment...${NC}"

# Create deployment package
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
PACKAGE_NAME="${PROJECT_NAME}_${TIMESTAMP}.zip"

echo -e "\n${BLUE}[>] Creating deployment package${NC}"
echo -e "${YELLOW}[i] Package: $PACKAGE_NAME${NC}"

# Create temporary directory for packaging
TEMP_DIR=$(mktemp -d)
cp -r "$LOCAL_PROJECT_PATH" "$TEMP_DIR/$PROJECT_NAME"

# Remove unnecessary files from package
echo -e "${BLUE}[>] Cleaning package (removing node_modules, dist, logs, etc.)${NC}"
cd "$TEMP_DIR"
find "$PROJECT_NAME" -name "node_modules" -type d -exec rm -rf {} + 2>/dev/null || true
find "$PROJECT_NAME" -name "dist" -type d -exec rm -rf {} + 2>/dev/null || true
find "$PROJECT_NAME" -name "logs" -type d -exec rm -rf {} + 2>/dev/null || true
find "$PROJECT_NAME" -name ".git" -type d -exec rm -rf {} + 2>/dev/null || true
find "$PROJECT_NAME" -name "*.log" -type f -delete 2>/dev/null || true
find "$PROJECT_NAME" -name ".DS_Store" -type f -delete 2>/dev/null || true

# Create the zip package
zip -r "$PACKAGE_NAME" "$PROJECT_NAME" >/dev/null
mv "$PACKAGE_NAME" "$OLDPWD/"
cd "$OLDPWD"
rm -rf "$TEMP_DIR"

echo -e "${GREEN}[✓] Package created: $PACKAGE_NAME${NC}"

# Transfer package to remote server
echo -e "\n${BLUE}[>] Transferring package to remote server${NC}"
remote_copy "$PACKAGE_NAME" "/tmp/$PACKAGE_NAME" "Uploading $PACKAGE_NAME"

# Setup remote directory and extract package
echo -e "\n${BLUE}[>] Setting up remote environment${NC}"
remote_exec "
    # Create project directory
    mkdir -p '$REMOTE_PATH'
    cd '$REMOTE_PATH'

    # Remove old deployment if exists
    if [ -d '$PROJECT_NAME' ]; then
        echo 'Backing up existing deployment...'
        mv '$PROJECT_NAME' '${PROJECT_NAME}_backup_$(date +%Y%m%d_%H%M%S)' 2>/dev/null || true
    fi

    # Extract new package
    echo 'Extracting package...'
    unzip -q '/tmp/$PACKAGE_NAME'

    # Clean up
    rm -f '/tmp/$PACKAGE_NAME'

    echo 'Package extracted successfully'
" "Setting up remote directory and extracting package"

# Get remote sudo password if using password authentication
if [ "$USE_SSH_KEY" = false ]; then
    echo -e "\n${YELLOW}[i] The remote deployment requires sudo access${NC}"
    read -s -p "Enter sudo password for ${REMOTE_USER} on remote server: " REMOTE_SUDO_PASSWORD
    echo

    if [ -z "$REMOTE_SUDO_PASSWORD" ]; then
        echo -e "${RED}[!] Sudo password cannot be empty${NC}"
        exit 1
    fi
fi

# Run remote deployment with sudo password handling
echo -e "\n${BLUE}[>] Running remote deployment${NC}"
echo -e "${YELLOW}[i] This will install Node.js, build the app, and configure services${NC}"

# Create a deployment script that handles sudo password
DEPLOY_SCRIPT="deploy_with_sudo.sh"
cat > "$DEPLOY_SCRIPT" << 'EOF'
#!/bin/bash
set -e

# Colors
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m"

PROJECT_NAME="focusbudget"
cd "/home/<USER>/focusbudget/$PROJECT_NAME"

echo -e "${BLUE}[>] Starting remote deployment${NC}"

# Make build script executable
chmod +x build-and-deploy.sh

# Check if sudo password is provided via environment
if [ -n "$SUDO_PASSWORD" ]; then
    echo -e "${YELLOW}[i] Using provided sudo password${NC}"
    # Run deployment with sudo password from stdin
    echo "$SUDO_PASSWORD" | sudo -S ./build-and-deploy.sh
else
    echo -e "${YELLOW}[i] Running deployment (you may be prompted for sudo password)${NC}"
    ./build-and-deploy.sh
fi

echo -e "${GREEN}[✓] Remote deployment completed${NC}"
EOF

# Transfer deployment script
remote_copy "$DEPLOY_SCRIPT" "/tmp/$DEPLOY_SCRIPT" "Uploading deployment script"

# Execute remote deployment
if [ "$USE_SSH_KEY" = true ]; then
    remote_exec "
        chmod +x '/tmp/$DEPLOY_SCRIPT'
        cd '$REMOTE_PATH/$PROJECT_NAME'
        '/tmp/$DEPLOY_SCRIPT'
        rm -f '/tmp/$DEPLOY_SCRIPT'
    " "Executing remote deployment"
else
    remote_exec "
        chmod +x '/tmp/$DEPLOY_SCRIPT'
        cd '$REMOTE_PATH/$PROJECT_NAME'
        SUDO_PASSWORD='$REMOTE_SUDO_PASSWORD' '/tmp/$DEPLOY_SCRIPT'
        rm -f '/tmp/$DEPLOY_SCRIPT'
    " "Executing remote deployment with sudo password"
fi

# Clean up local files
rm -f "$PACKAGE_NAME" "$DEPLOY_SCRIPT"

echo -e "\n${GREEN}==============================="
echo -e " Deployment Completed!"
echo -e "===============================${NC}"
echo -e "${GREEN}[✓] FocusBudget deployed successfully${NC}"
echo -e "${YELLOW}[i] Access points:${NC}"
echo -e "${YELLOW}  • Main app: http://${REMOTE_HOST}${NC}"
echo -e "${YELLOW}  • Direct: http://${REMOTE_HOST}:3001${NC}"
echo -e "${YELLOW}  • API: http://${REMOTE_HOST}/api${NC}"
echo -e "${YELLOW}  • Health: http://${REMOTE_HOST}/health${NC}"

echo -e "\n${BLUE}Management commands:${NC}"
echo -e "${YELLOW}• Check status: ./quick-redeploy-new.sh (option 3)${NC}"
echo -e "${YELLOW}• View logs: ./quick-redeploy-new.sh (option 4)${NC}"
echo -e "${YELLOW}• Restart: ./quick-redeploy-new.sh (option 2)${NC}"

echo -e "\n${GREEN}Deployment completed successfully!${NC}"
