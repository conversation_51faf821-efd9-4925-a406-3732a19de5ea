#!/bin/bash

# BudgetPage Deployment Test Script
# This script tests the deployed application to ensure it's working correctly

# Colors for better readability
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

echo -e "\n${BLUE}==============================="
echo -e " BudgetPage Deployment Test"
echo -e "===============================${NC}\n"

# Test 1: Check if service is running
echo -e "${BLUE}[>] Testing systemd service status${NC}"
if systemctl is-active --quiet budgetpage; then
  echo -e "${GREEN}[✓] BudgetPage service is running${NC}"
else
  echo -e "${RED}[✗] BudgetPage service is not running${NC}"
  echo -e "${YELLOW}[i] Try: sudo systemctl start budgetpage${NC}"
fi

# Test 2: Check if port 3001 is listening
echo -e "\n${BLUE}[>] Testing port availability${NC}"
if netstat -tuln 2>/dev/null | grep -q ":3001 " || ss -tuln 2>/dev/null | grep -q ":3001 "; then
  echo -e "${GREEN}[✓] Port 3001 is listening${NC}"
else
  echo -e "${RED}[✗] Port 3001 is not listening${NC}"
  echo -e "${YELLOW}[i] Check service logs: sudo journalctl -u budgetpage -f${NC}"
fi

# Test 3: Test HTTP response
echo -e "\n${BLUE}[>] Testing HTTP response${NC}"
if command -v curl >/dev/null 2>&1; then
  HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3001/ || echo "000")
  if [ "$HTTP_STATUS" = "200" ]; then
    echo -e "${GREEN}[✓] HTTP response successful (200)${NC}"
  else
    echo -e "${RED}[✗] HTTP response failed (Status: $HTTP_STATUS)${NC}"
  fi
else
  echo -e "${YELLOW}[i] curl not available, skipping HTTP test${NC}"
fi

# Test 4: Test API endpoint (direct)
echo -e "\n${BLUE}[>] Testing API endpoint (direct)${NC}"
if command -v curl >/dev/null 2>&1; then
  API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3001/api/storage/status || echo "000")
  if [ "$API_STATUS" = "200" ]; then
    echo -e "${GREEN}[✓] Direct API endpoint responding (200)${NC}"
  else
    echo -e "${RED}[✗] Direct API endpoint failed (Status: $API_STATUS)${NC}"
  fi
else
  echo -e "${YELLOW}[i] curl not available, skipping API test${NC}"
fi

# Test 5: Test Nginx service
echo -e "\n${BLUE}[>] Testing Nginx service${NC}"
if systemctl is-active --quiet nginx; then
  echo -e "${GREEN}[✓] Nginx service is running${NC}"
else
  echo -e "${RED}[✗] Nginx service is not running${NC}"
  echo -e "${YELLOW}[i] Try: sudo systemctl start nginx${NC}"
fi

# Test 6: Test Nginx proxy (port 80)
echo -e "\n${BLUE}[>] Testing Nginx reverse proxy${NC}"
if command -v curl >/dev/null 2>&1; then
  NGINX_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost/ || echo "000")
  if [ "$NGINX_STATUS" = "200" ]; then
    echo -e "${GREEN}[✓] Nginx proxy responding (200)${NC}"
  else
    echo -e "${RED}[✗] Nginx proxy failed (Status: $NGINX_STATUS)${NC}"
  fi

  # Test API through Nginx
  NGINX_API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost/api/storage/status || echo "000")
  if [ "$NGINX_API_STATUS" = "200" ]; then
    echo -e "${GREEN}[✓] Nginx API proxy responding (200)${NC}"
  else
    echo -e "${RED}[✗] Nginx API proxy failed (Status: $NGINX_API_STATUS)${NC}"
  fi
else
  echo -e "${YELLOW}[i] curl not available, skipping Nginx proxy test${NC}"
fi

# Test 7: Check port 80 availability
echo -e "\n${BLUE}[>] Testing port 80 availability${NC}"
if netstat -tuln 2>/dev/null | grep -q ":80 " || ss -tuln 2>/dev/null | grep -q ":80 "; then
  echo -e "${GREEN}[✓] Port 80 is listening (Nginx)${NC}"
else
  echo -e "${RED}[✗] Port 80 is not listening${NC}"
  echo -e "${YELLOW}[i] Check Nginx status: sudo systemctl status nginx${NC}"
fi

# Test 8: Check data directory
echo -e "\n${BLUE}[>] Testing data directory${NC}"
DATA_DIR="./data"
if [ -d "$DATA_DIR" ]; then
  echo -e "${GREEN}[✓] Data directory exists: $DATA_DIR${NC}"
  if [ -w "$DATA_DIR" ]; then
    echo -e "${GREEN}[✓] Data directory is writable${NC}"
  else
    echo -e "${RED}[✗] Data directory is not writable${NC}"
  fi
else
  echo -e "${RED}[✗] Data directory does not exist: $DATA_DIR${NC}"
fi

# Test 9: Check environment file
echo -e "\n${BLUE}[>] Testing environment configuration${NC}"
ENV_FILE="./.env"
if [ -f "$ENV_FILE" ]; then
  echo -e "${GREEN}[✓] Environment file exists: $ENV_FILE${NC}"
  if grep -q "DATA_DIR" "$ENV_FILE"; then
    echo -e "${GREEN}[✓] DATA_DIR configured in environment${NC}"
  else
    echo -e "${YELLOW}[i] DATA_DIR not found in environment file${NC}"
  fi
else
  echo -e "${RED}[✗] Environment file does not exist: $ENV_FILE${NC}"
fi

# Test 10: Check logs directory
echo -e "\n${BLUE}[>] Testing logs directory${NC}"
LOGS_DIR="./logs"
if [ -d "$LOGS_DIR" ]; then
  echo -e "${GREEN}[✓] Logs directory exists: $LOGS_DIR${NC}"
else
  echo -e "${YELLOW}[i] Logs directory does not exist: $LOGS_DIR${NC}"
fi

# Test 11: Check build output
echo -e "\n${BLUE}[>] Testing build output${NC}"
DIST_DIR="./dist"
if [ -d "$DIST_DIR" ]; then
  echo -e "${GREEN}[✓] Build directory exists: $DIST_DIR${NC}"
  if [ -f "$DIST_DIR/index.html" ]; then
    echo -e "${GREEN}[✓] Frontend build files present${NC}"
  else
    echo -e "${RED}[✗] Frontend build files missing${NC}"
  fi
else
  echo -e "${RED}[✗] Build directory does not exist: $DIST_DIR${NC}"
fi

# Test 12: Check server file
echo -e "\n${BLUE}[>] Testing server file${NC}"
SERVER_FILE="./server.cjs"
if [ -f "$SERVER_FILE" ]; then
  echo -e "${GREEN}[✓] Server file exists: $SERVER_FILE${NC}"
else
  echo -e "${RED}[✗] Server file does not exist: $SERVER_FILE${NC}"
fi

# Summary
echo -e "\n${BLUE}==============================="
echo -e " Test Summary"
echo -e "===============================${NC}"

if systemctl is-active --quiet budgetpage && systemctl is-active --quiet nginx && [ -d "$DATA_DIR" ] && [ -f "$ENV_FILE" ]; then
  echo -e "${GREEN}[✓] Full deployment appears successful${NC}"
  echo -e "${YELLOW}[i] Primary access: http://localhost (via Nginx)${NC}"
  echo -e "${YELLOW}[i] Direct access: http://localhost:3001${NC}"
  echo -e "${YELLOW}[i] API access: http://localhost/api/storage/status${NC}"
else
  echo -e "${RED}[✗] Deployment has issues that need attention${NC}"
  echo -e "${YELLOW}[i] Check the failed tests above and fix accordingly${NC}"
fi

echo -e "\n${BLUE}Quick Commands:${NC}"
echo -e "${YELLOW}• BudgetPage status: sudo systemctl status budgetpage${NC}"
echo -e "${YELLOW}• BudgetPage logs: sudo journalctl -u budgetpage -f${NC}"
echo -e "${YELLOW}• Nginx status: sudo systemctl status nginx${NC}"
echo -e "${YELLOW}• Nginx logs: sudo journalctl -u nginx -f${NC}"
echo -e "${YELLOW}• Restart BudgetPage: sudo systemctl restart budgetpage${NC}"
echo -e "${YELLOW}• Restart Nginx: sudo systemctl restart nginx${NC}"
echo -e "${YELLOW}• Test via Nginx: curl http://localhost/api/storage/status${NC}"
echo -e "${YELLOW}• Test direct: curl http://localhost:3001/api/storage/status${NC}"
