import React, { useState, useEffect } from 'react';
import { TabType } from './types';
import { storage } from './utils/storage';
import { licenseManager } from './utils/licenseManager';
import TabNavigation from './components/TabNavigation';
import SpendingTab from './tabs/SpendingTab';
import BudgetTab from './tabs/BudgetTab';
import TrackingTab from './tabs/TrackingTab';
import ForecastTab from './tabs/ForecastTab';
import HistoryTab from './tabs/HistoryTab';
import AdminTab from './tabs/AdminTab';
import DataTab from './tabs/DataTab';
import { DollarSign, Moon, Sun, Loader2, AlertTriangle } from 'lucide-react';

function App() {
  const [activeTab, setActiveTab] = useState<TabType>('spending');
  const [darkMode, setDarkMode] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [licenseWarning, setLicenseWarning] = useState<string | null>(null);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Initialize storage and license manager
      await storage.init();
      await licenseManager.initialize();
      
      // Load settings
      const settings = await storage.getSettings();
      setDarkMode(settings.darkMode);

      // Check license status and show warnings if needed
      checkLicenseStatus();
    } catch (err) {
      console.error('Failed to initialize app:', err);
      setError('Failed to initialize application. Some features may not work properly.');
      
      // Fallback to localStorage settings
      try {
        const fallbackSettings = JSON.parse(localStorage.getItem('budget_settings') || '{}');
        setDarkMode(fallbackSettings.darkMode ?? true);
      } catch {
        setDarkMode(true);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const checkLicenseStatus = async () => {
    try {
      const transactions = await storage.getTransactions();
      const dataCheck = licenseManager.isDataWithinLimits(transactions);
      
      if (!dataCheck.withinLimits) {
        setLicenseWarning(dataCheck.message || 'Data exceeds license limits');
      } else {
        setLicenseWarning(null);
      }
    } catch (err) {
      console.error('Failed to check license status:', err);
    }
  };

  const toggleDarkMode = async () => {
    const newDarkMode = !darkMode;
    setDarkMode(newDarkMode);
    
    try {
      const settings = await storage.getSettings();
      await storage.saveSettings({ ...settings, darkMode: newDarkMode });
    } catch (err) {
      console.error('Failed to save dark mode setting:', err);
      // Continue with UI update even if save fails
    }
  };

  const handleTabChange = (tab: TabType) => {
    // Check license restrictions for certain tabs
    if (tab === 'forecast' && !licenseManager.canAccessForecast()) {
      setError('Forecast feature requires a valid license. Please activate your license in the Admin tab.');
      return;
    }
    
    setActiveTab(tab);
    setError(null);
    
    // Clear any previous license warnings when changing tabs
    // The warning will be re-evaluated if needed
    setLicenseWarning(null);
  };

  const renderActiveTab = () => {
    switch (activeTab) {
      case 'spending':
        return <SpendingTab />;
      case 'budget':
        return <BudgetTab />;
      case 'tracking':
        return <TrackingTab />;
      case 'forecast':
        if (!licenseManager.canAccessForecast()) {
          return (
            <div className="flex items-center justify-center py-12">
              <div className="text-center max-w-md">
                <AlertTriangle className="h-16 w-16 text-yellow-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">Forecast Feature Locked</h3>
                <p className="text-gray-400 mb-4">
                  The forecast feature requires a valid license. Activate your license in the Admin tab to access advanced forecasting capabilities.
                </p>
                <button
                  onClick={() => setActiveTab('admin')}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  Go to License Management
                </button>
              </div>
            </div>
          );
        }
        return <ForecastTab />;
      case 'history':
        return <HistoryTab />;
      case 'admin':
        return <AdminTab />;
      case 'data':
        return <DataTab />;
      default:
        return <SpendingTab />;
    }
  };

  if (isLoading) {
    return (
      <div className={`min-h-screen flex items-center justify-center ${
        darkMode ? 'bg-gray-900' : 'bg-gray-50'
      }`}>
        <div className="text-center">
          <Loader2 className={`h-12 w-12 animate-spin mx-auto mb-4 ${
            darkMode ? 'text-blue-400' : 'text-blue-600'
          }`} />
          <p className={`text-lg ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Initializing Budget App...
          </p>
          <p className={`text-sm mt-2 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Setting up storage and checking license
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen transition-colors ${
      darkMode ? 'bg-gray-900' : 'bg-gray-50'
    }`}>
      {/* License Warning Banner */}
      {licenseWarning && (
        <div className="bg-yellow-600 text-white px-4 py-2 text-center text-sm">
          <div className="flex items-center justify-center space-x-2">
            <AlertTriangle size={16} />
            <span>{licenseWarning}</span>
            <button
              onClick={() => setActiveTab('admin')}
              className="underline hover:no-underline ml-2"
            >
              Activate License
            </button>
          </div>
        </div>
      )}

      {/* Error Banner */}
      {error && (
        <div className="bg-red-600 text-white px-4 py-2 text-center text-sm">
          <div className="flex items-center justify-center space-x-2">
            <AlertTriangle size={16} />
            <span>{error}</span>
            <button
              onClick={() => setError(null)}
              className="ml-2 text-red-200 hover:text-white"
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* Header */}
      <div className={`border-b transition-colors ${
        darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
      }`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${
                darkMode ? 'bg-blue-500/20' : 'bg-blue-500/10'
              }`}>
                <DollarSign className={`h-8 w-8 ${
                  darkMode ? 'text-blue-400' : 'text-blue-600'
                }`} />
              </div>
              <div>
                <h1 className={`text-xl font-bold ${
                  darkMode ? 'text-white' : 'text-gray-900'
                }`}>
                  Personal Budget
                </h1>
                <div className="flex items-center space-x-2">
                  <p className={`text-sm ${
                    darkMode ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    Smart household budgeting made simple
                  </p>
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    licenseManager.isLicensed()
                      ? 'bg-green-500/20 text-green-400'
                      : 'bg-red-500/20 text-red-400'
                  }`}>
                    {licenseManager.isLicensed() ? 'Licensed' : 'Unlicensed'}
                  </span>
                </div>
              </div>
            </div>
            
            <button
              onClick={toggleDarkMode}
              className={`p-2 rounded-lg transition-colors ${
                darkMode 
                  ? 'text-gray-400 hover:text-white hover:bg-gray-700' 
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              {darkMode ? <Sun size={20} /> : <Moon size={20} />}
            </button>
          </div>
          
          <TabNavigation activeTab={activeTab} onTabChange={handleTabChange} />
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderActiveTab()}
      </div>
    </div>
  );
}

export default App;