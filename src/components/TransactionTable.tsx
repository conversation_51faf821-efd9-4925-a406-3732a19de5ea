
import { Transaction, BudgetCategory } from '../types';
import { TrendingUp, TrendingDown, AlertCircle } from 'lucide-react';

interface TransactionTableProps {
  transactions: Transaction[];
  budgetCategories: BudgetCategory[];
  showOnlyLatestImport?: boolean;
  maxTransactions?: number;
}

export default function TransactionTable({ 
  transactions, 
  budgetCategories,
  showOnlyLatestImport = true,
  maxTransactions = 100
}: TransactionTableProps) {
  // Track if we're displaying limited data as a local variable instead of state to avoid render loops
  const getCategoryBudget = (categoryName: string) => {
    return budgetCategories.find(cat => cat.name === categoryName);
  };

  const getCategorySpending = (categoryName: string) => {
    return transactions
      .filter(t => t.category === categoryName && t.amount < 0)
      .reduce((sum, t) => sum + Math.abs(t.amount), 0);
  };

  const renderCategoryIndicator = (categoryName: string) => {
    const budget = getCategoryBudget(categoryName);
    const spending = getCategorySpending(categoryName);
    
    if (!budget) return null;

    const isOverBudget = spending > budget.budgetAmount;
    
    return (
      <div className="flex items-center space-x-1">
        {isOverBudget ? (
          <TrendingUp className="text-red-400" size={16} />
        ) : (
          <TrendingDown className="text-green-400" size={16} />
        )}
        <span className={`text-xs ${isOverBudget ? 'text-red-400' : 'text-green-400'}`}>
          ${spending.toFixed(2)} / ${budget.budgetAmount.toFixed(2)}
        </span>
      </div>
    );
  };

  if (transactions.length === 0) {
    return (
      <div className="text-center py-8 text-gray-400">
        <p>No transactions to display</p>
        <p className="text-sm mt-1">Upload a CSV file to get started</p>
      </div>
    );
  }

  // Filter to just the latest import if requested
  let filteredTransactions = transactions;
  
  if (showOnlyLatestImport && transactions.length > 0) {
    // Group transactions by original data source
    const uniqueImports = new Set<string>();
    transactions.forEach(t => {
      if (t.originalData) uniqueImports.add(t.originalData);
    });
    
    // Find the most recent import timestamp
    let latestImportData = '';
    let latestTimestamp = 0;
    
    uniqueImports.forEach(importData => {
      const importTransactions = transactions.filter(t => t.originalData === importData);
      if (importTransactions.length > 0) {
        // Extract timestamp from id (format: timestamp-index)
        const idParts = importTransactions[0].id.split('-');
        if (idParts.length > 0) {
          const timestamp = parseInt(idParts[0]);
          if (timestamp > latestTimestamp) {
            latestTimestamp = timestamp;
            latestImportData = importData;
          }
        }
      }
    });
    
    // Filter to just the latest import
    if (latestImportData) {
      filteredTransactions = transactions.filter(t => t.originalData === latestImportData);
    }
  }
  
  // Enforce transaction limit for performance
  const totalTransactions = filteredTransactions.length;
  const isLimited = totalTransactions > maxTransactions;
  
  if (isLimited) {
    filteredTransactions = filteredTransactions
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, maxTransactions);
  }
  
  // Group transactions by category for better organization
  const transactionsByCategory = filteredTransactions.reduce((acc, transaction) => {
    if (!acc[transaction.category]) {
      acc[transaction.category] = [];
    }
    acc[transaction.category].push(transaction);
    return acc;
  }, {} as Record<string, Transaction[]>);

  return (
    <div className="space-y-6">
      {isLimited && (
        <div className="flex items-center space-x-2 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg text-yellow-400 mb-4">
          <AlertCircle size={18} />
          <span>Showing only the first {maxTransactions} transactions for performance reasons. Total: {totalTransactions}</span>
        </div>
      )}
      {showOnlyLatestImport && filteredTransactions.length < transactions.length && (
        <div className="flex items-center space-x-2 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg text-blue-400 mb-4">
          <AlertCircle size={18} />
          <span>Showing only the most recent import ({filteredTransactions.length} of {transactions.length} total transactions)</span>
        </div>
      )}
      {Object.entries(transactionsByCategory).map(([categoryName, categoryTransactions]) => (
        <div key={categoryName} className="bg-gray-800 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-lg font-medium text-white">{categoryName}</h3>
            {renderCategoryIndicator(categoryName)}
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="text-gray-400 border-b border-gray-700">
                  <th className="text-left py-2">Date</th>
                  <th className="text-left py-2">Description</th>
                  <th className="text-right py-2">Amount</th>
                </tr>
              </thead>
              <tbody>
                {categoryTransactions
                  .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
                  .map((transaction) => (
                    <tr key={transaction.id} className="border-b border-gray-700/50">
                      <td className="py-2 text-gray-300">
                        {new Date(transaction.date).toLocaleDateString()}
                      </td>
                      <td className="py-2 text-gray-300">{transaction.description}</td>
                      <td className={`py-2 text-right font-medium ${
                        transaction.amount < 0 ? 'text-red-400' : 'text-green-400'
                      }`}>
                        {transaction.amount < 0 ? '-' : '+'}${Math.abs(transaction.amount).toFixed(2)}
                      </td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
        </div>
      ))}
    </div>
  );
}