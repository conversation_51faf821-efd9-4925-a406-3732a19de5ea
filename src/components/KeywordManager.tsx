import React, { useState } from 'react';
import { KeywordMapping, BudgetCategory } from '../types';
import { Plus, Edit2, Trash2, Check, X, Tag } from 'lucide-react';

interface KeywordManagerProps {
  keywords: KeywordMapping[];
  categories: BudgetCategory[];
  onAddKeyword: (keyword: string, categoryName: string) => void;
  onRemoveKeyword: (keywordId: string) => void;
  onUpdateKeyword: (keywordId: string, keyword: string, categoryName: string) => void;
}

export default function KeywordManager({ 
  keywords, 
  categories,
  onAddKeyword, 
  onRemoveKeyword, 
  onUpdateKeyword 
}: KeywordManagerProps) {
  const [isAdding, setIsAdding] = useState(false);
  const [newKeyword, setNewKeyword] = useState('');
  const [newCategory, setNewCategory] = useState('');
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingKeyword, setEditingKeyword] = useState('');
  const [editingCategory, setEditingCategory] = useState('');

  const handleAddKeyword = () => {
    if (newKeyword.trim() && newCategory) {
      onAddKeyword(newKeyword.trim(), newCategory);
      setNewKeyword('');
      setNewCategory('');
      setIsAdding(false);
    }
  };

  const handleStartEdit = (mapping: KeywordMapping) => {
    setEditingId(mapping.id);
    setEditingKeyword(mapping.keyword);
    setEditingCategory(mapping.categoryName);
  };

  const handleSaveEdit = () => {
    if (editingId && editingKeyword.trim() && editingCategory) {
      onUpdateKeyword(editingId, editingKeyword.trim(), editingCategory);
      setEditingId(null);
      setEditingKeyword('');
      setEditingCategory('');
    }
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    setEditingKeyword('');
    setEditingCategory('');
  };

  const handleRemoveKeyword = (keywordId: string) => {
    if (confirm('Are you sure you want to remove this keyword mapping?')) {
      onRemoveKeyword(keywordId);
    }
  };

  const sortedKeywords = [...keywords].sort((a, b) => a.keyword.localeCompare(b.keyword));

  return (
    <div className="bg-gray-800 rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-lg font-medium text-white flex items-center space-x-2">
            <Tag size={20} />
            <span>Keyword to Category Mappings</span>
          </h2>
          <p className="text-sm text-gray-400 mt-1">
            Define keywords that should strongly influence AI categorization decisions
          </p>
        </div>
        <button
          onClick={() => setIsAdding(true)}
          className="flex items-center space-x-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
        >
          <Plus size={16} />
          <span>Add Keyword</span>
        </button>
      </div>

      {keywords.length === 0 && !isAdding && (
        <div className="text-center py-8 text-gray-400">
          <Tag className="mx-auto h-12 w-12 text-gray-500 mb-3" />
          <p>No keyword mappings defined</p>
          <p className="text-sm mt-1">Add keywords to improve AI categorization accuracy</p>
        </div>
      )}

      <div className="space-y-3">
        {sortedKeywords.map((mapping) => (
          <div key={mapping.id} className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
            {editingId === mapping.id ? (
              <div className="flex items-center space-x-3 flex-1">
                <div className="flex-1">
                  <input
                    type="text"
                    placeholder="Keyword"
                    value={editingKeyword}
                    onChange={(e) => setEditingKeyword(e.target.value)}
                    className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div className="flex-1">
                  <select
                    value={editingCategory}
                    onChange={(e) => setEditingCategory(e.target.value)}
                    className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select Category</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.name}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleSaveEdit}
                    className="p-2 text-green-400 hover:text-green-300"
                  >
                    <Check size={16} />
                  </button>
                  <button
                    onClick={handleCancelEdit}
                    className="p-2 text-red-400 hover:text-red-300"
                  >
                    <X size={16} />
                  </button>
                </div>
              </div>
            ) : (
              <>
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <span className="px-3 py-1 bg-blue-500/20 text-blue-300 rounded-full text-sm font-medium">
                      "{mapping.keyword}"
                    </span>
                    <span className="text-gray-400">→</span>
                    <span className="px-3 py-1 bg-green-500/20 text-green-300 rounded-full text-sm font-medium">
                      {mapping.categoryName}
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Added {new Date(mapping.createdAt).toLocaleDateString()}
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleStartEdit(mapping)}
                    className="p-2 text-gray-400 hover:text-white transition-colors"
                  >
                    <Edit2 size={16} />
                  </button>
                  <button
                    onClick={() => handleRemoveKeyword(mapping.id)}
                    className="p-2 text-gray-400 hover:text-red-400 transition-colors"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
              </>
            )}
          </div>
        ))}

        {isAdding && (
          <div className="flex items-center space-x-3 p-4 bg-gray-700 rounded-lg">
            <div className="flex-1">
              <input
                type="text"
                placeholder="Enter keyword (e.g., 'starbucks', 'gas station')"
                value={newKeyword}
                onChange={(e) => setNewKeyword(e.target.value)}
                className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                onKeyPress={(e) => e.key === 'Enter' && newCategory && handleAddKeyword()}
                autoFocus
              />
            </div>
            <div className="flex-1">
              <select
                value={newCategory}
                onChange={(e) => setNewCategory(e.target.value)}
                className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select Category</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.name}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={handleAddKeyword}
                disabled={!newKeyword.trim() || !newCategory}
                className="p-2 text-green-400 hover:text-green-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Check size={16} />
              </button>
              <button
                onClick={() => {
                  setIsAdding(false);
                  setNewKeyword('');
                  setNewCategory('');
                }}
                className="p-2 text-red-400 hover:text-red-300"
              >
                <X size={16} />
              </button>
            </div>
          </div>
        )}
      </div>

      <div className="mt-6 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
        <h4 className="text-blue-300 font-medium mb-2">How Keyword Mappings Work</h4>
        <ul className="text-sm text-blue-200 space-y-1">
          <li>• Keywords are matched against transaction descriptions (case-insensitive)</li>
          <li>• AI will strongly consider the mapped category when the keyword is found</li>
          <li>• Use specific keywords for better accuracy (e.g., "starbucks" vs "coffee")</li>
          <li>• Keywords are included in every AI categorization request</li>
        </ul>
      </div>
    </div>
  );
}