import { Transaction, BudgetCategory, MonthlyHistory, KeywordMapping, TrackedExpense, LicenseInfo } from '../types';

interface DBSchema {
  transactions: Transaction;
  budgetCategories: BudgetCategory;
  monthlyHistory: MonthlyHistory;
  keywordMappings: KeywordMapping;
  trackedExpenses: TrackedExpense;
  license: LicenseInfo & { id: string };
  settings: { id: string; darkMode: boolean; apiKey: string };
}

class IndexedDBStorage {
  private dbName = 'BudgetAppDB';
  private version = 3; // Incremented for new license store
  private db: IDBDatabase | null = null;

  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // Create object stores
        if (!db.objectStoreNames.contains('transactions')) {
          const transactionStore = db.createObjectStore('transactions', { keyPath: 'id' });
          transactionStore.createIndex('date', 'date');
          transactionStore.createIndex('category', 'category');
        }

        if (!db.objectStoreNames.contains('budgetCategories')) {
          db.createObjectStore('budgetCategories', { keyPath: 'id' });
        }

        if (!db.objectStoreNames.contains('monthlyHistory')) {
          const historyStore = db.createObjectStore('monthlyHistory', { keyPath: ['month', 'year'] });
          historyStore.createIndex('year', 'year');
        }

        if (!db.objectStoreNames.contains('keywordMappings')) {
          db.createObjectStore('keywordMappings', { keyPath: 'id' });
        }

        if (!db.objectStoreNames.contains('trackedExpenses')) {
          const trackedStore = db.createObjectStore('trackedExpenses', { keyPath: 'id' });
          trackedStore.createIndex('isActive', 'isActive');
          trackedStore.createIndex('frequency', 'frequency');
        }

        if (!db.objectStoreNames.contains('license')) {
          db.createObjectStore('license', { keyPath: 'id' });
        }

        if (!db.objectStoreNames.contains('settings')) {
          db.createObjectStore('settings', { keyPath: 'id' });
        }
      };
    });
  }

  private async ensureDB(): Promise<IDBDatabase> {
    if (!this.db) {
      await this.init();
    }
    return this.db!;
  }

  async getAll<T>(storeName: string): Promise<T[]> {
    const db = await this.ensureDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.getAll();

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
    });
  }

  async get<T>(storeName: string, key: any): Promise<T | undefined> {
    const db = await this.ensureDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.get(key);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
    });
  }

  async put<T>(storeName: string, data: T): Promise<void> {
    const db = await this.ensureDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.put(data);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  }

  async putAll<T>(storeName: string, data: T[]): Promise<void> {
    const db = await this.ensureDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      
      let completed = 0;
      const total = data.length;

      if (total === 0) {
        resolve();
        return;
      }

      data.forEach(item => {
        const request = store.put(item);
        request.onerror = () => reject(request.error);
        request.onsuccess = () => {
          completed++;
          if (completed === total) {
            resolve();
          }
        };
      });
    });
  }

  async delete(storeName: string, key: any): Promise<void> {
    const db = await this.ensureDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.delete(key);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  }

  async clear(storeName: string): Promise<void> {
    const db = await this.ensureDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.clear();

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  }

  async clearAll(): Promise<void> {
    const storeNames = ['transactions', 'budgetCategories', 'monthlyHistory', 'keywordMappings', 'trackedExpenses', 'license', 'settings'];
    await Promise.all(storeNames.map(storeName => this.clear(storeName)));
  }
}

export const indexedDBStorage = new IndexedDBStorage();