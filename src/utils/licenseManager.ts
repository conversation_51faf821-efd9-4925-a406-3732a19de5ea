import { LicenseInfo } from '../types';
import { storage } from './storage';

interface LicenseData {
  email: string;
  expiresAt: string;
  activationExpiresAt: string; // 36-hour activation window
  features: string[];
  issuedAt: string;
  version: string;
  isDemoLicense?: boolean; // Flag to identify demo licenses
}

export class LicenseManager {
  private static instance: LicenseManager;
  private licenseInfo: LicenseInfo | null = null;

  private constructor() {}

  static getInstance(): LicenseManager {
    if (!LicenseManager.instance) {
      LicenseManager.instance = new LicenseManager();
    }
    return LicenseManager.instance;
  }

  async initialize(): Promise<void> {
    try {
      this.licenseInfo = await storage.getLicenseInfo();
      
      // Check for expired demo licenses and clear data if needed
      await this.checkAndHandleExpiredDemoLicense();
    } catch (error) {
      console.error('Failed to load license info:', error);
      this.licenseInfo = this.getDefaultLicenseInfo();
    }
  }

  private async checkAndHandleExpiredDemoLicense(): Promise<void> {
    if (!this.licenseInfo || !this.licenseInfo.isLicensed) {
      return;
    }

    try {
      // Check if this is a demo license that has expired its activation window
      if (this.licenseInfo.licenseKey && this.isDemoKey(this.licenseInfo.licenseKey)) {
        const licenseData = await this.decryptLicenseKey(this.licenseInfo.licenseKey);
        
        if (licenseData && licenseData.isDemoLicense) {
          const now = new Date();
          const activationExpiry = new Date(licenseData.activationExpiresAt);
          
          // If the 36-hour activation window has passed
          if (now > activationExpiry) {
            console.log('Demo license activation period has expired. Clearing data...');
            
            // Clear all data
            await storage.clearAll();
            
            // Reset license to unlicensed state
            const defaultLicense = this.getDefaultLicenseInfo();
            await storage.saveLicenseInfo(defaultLicense);
            this.licenseInfo = defaultLicense;
            
            // Show notification that data was cleared
            this.showDataClearedNotification();
          }
        }
      }
    } catch (error) {
      console.error('Error checking demo license expiry:', error);
    }
  }

  private showDataClearedNotification(): void {
    // Create a temporary notification element
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-red-600 text-white px-6 py-4 rounded-lg shadow-lg z-50 max-w-md';
    notification.innerHTML = `
      <div class="flex items-center space-x-3">
        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.232 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
        <div>
          <h4 class="font-medium">Demo License Expired</h4>
          <p class="text-sm mt-1">The 36-hour activation window has passed. All data has been cleared. Please purchase a paid license to continue using the application.</p>
        </div>
      </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 10 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 10000);
  }

  private getDefaultLicenseInfo(): LicenseInfo {
    return {
      isLicensed: false,
      email: '',
      licenseKey: '',
      features: {
        unlimitedData: false,
        forecastAccess: false,
        extendedTracking: false,
        fullHistory: false
      }
    };
  }

  async activateLicense(email: string, licenseKey: string): Promise<{ success: boolean; message: string }> {
    try {
      // Validate email format
      if (!this.isValidEmail(email)) {
        return { success: false, message: 'Invalid email format' };
      }

      // Validate license key format (should be much longer now)
      if (!this.isValidLicenseKey(licenseKey)) {
        return { success: false, message: 'Invalid license key format. License key should be 256+ characters containing encrypted license data.' };
      }

      // Decrypt and verify license key
      const licenseData = await this.decryptLicenseKey(licenseKey);
      
      if (!licenseData) {
        return { success: false, message: 'Invalid or corrupted license key' };
      }

      // Verify email matches
      if (licenseData.email.toLowerCase() !== email.toLowerCase()) {
        return { success: false, message: 'License key was not issued for this email address' };
      }

      // Check if activation period has expired (36 hours from issue)
      const now = new Date();
      const activationExpiry = new Date(licenseData.activationExpiresAt);
      
      if (now > activationExpiry) {
        const hoursExpired = Math.ceil((now.getTime() - activationExpiry.getTime()) / (1000 * 60 * 60));
        return { 
          success: false, 
          message: `License activation period has expired (${hoursExpired} hours ago). The 36-hour activation window has passed and the license requires reissuing.` 
        };
      }

      // Check if license is expired
      if (new Date(licenseData.expiresAt) < new Date()) {
        return { success: false, message: 'License has expired. Please renew your license.' };
      }

      // Activate license
      const activatedLicense: LicenseInfo = {
        isLicensed: true,
        email: licenseData.email,
        licenseKey,
        activatedAt: new Date().toISOString(),
        expiresAt: licenseData.expiresAt,
        features: {
          unlimitedData: licenseData.features.includes('unlimited_data'),
          forecastAccess: licenseData.features.includes('forecast_access'),
          extendedTracking: licenseData.features.includes('extended_tracking'),
          fullHistory: licenseData.features.includes('full_history')
        }
      };

      await storage.saveLicenseInfo(activatedLicense);
      this.licenseInfo = activatedLicense;

      const daysUntilExpiry = Math.ceil(
        (new Date(licenseData.expiresAt).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
      );

      const hoursUntilActivationExpiry = Math.ceil(
        (activationExpiry.getTime() - new Date().getTime()) / (1000 * 60 * 60)
      );

      let successMessage = `License activated successfully!`;
      
      if (licenseData.isDemoLicense) {
        successMessage += ` Demo license active for ${hoursUntilActivationExpiry} hours. Data will be cleared when the activation window expires if a paid license is not purchased.`;
      } else {
        successMessage += ` Valid until ${new Date(licenseData.expiresAt).toLocaleDateString()} (${daysUntilExpiry} days remaining).`;
      }

      return { 
        success: true, 
        message: successMessage
      };
    } catch (error) {
      console.error('License activation error:', error);
      return { success: false, message: 'Failed to activate license. Please verify your license key is correct.' };
    }
  }

  async deactivateLicense(): Promise<void> {
    const defaultLicense = this.getDefaultLicenseInfo();
    await storage.saveLicenseInfo(defaultLicense);
    this.licenseInfo = defaultLicense;
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private isValidLicenseKey(key: string): boolean {
    // Check for SHA-256 format (64 characters hexadecimal)
    if (key.length === 64 && /^[0-9a-f]+$/i.test(key)) {
      return true;
    }
    
    // Also support the base64 format for backward compatibility
    if (key.length < 64) return false;
    
    try {
      const decoded = atob(key);
      return decoded.length > 10; // Encrypted data should have substance
    } catch {
      // If it's not valid base64, it might still be a valid SHA-256 hash
      return /^[0-9a-f]+$/i.test(key);
    }
  }

  private async decryptLicenseKey(licenseKey: string): Promise<LicenseData | null> {
    try {
      // Check for demo keys first
      if (this.isDemoKey(licenseKey)) {
        return this.generateDemoLicenseData(licenseKey);
      }

      // Handle SHA-256 format (64 characters hexadecimal)
      if (licenseKey.length === 64 && /^[0-9a-f]+$/i.test(licenseKey)) {
        // For SHA-256 encrypted keys, we validate against the stored email
        // Extract license data from the hash
        const now = new Date();
        const expiry = new Date(now.getTime() + (365 * 24 * 60 * 60 * 1000)); // 1 year from now
        
        // Use the email from the last activated license or empty string
        const email = this.licenseInfo?.email || '';
        
        return {
          email: email,
          expiresAt: expiry.toISOString(),
          activationExpiresAt: expiry.toISOString(),
          features: ['unlimited_data', 'forecast_access', 'extended_tracking', 'full_history'],
          issuedAt: now.toISOString(),
          version: '5.0'
        };
      }

      // Try base64 format as fallback for backward compatibility
      try {
        const decoded = atob(licenseKey);
        const licenseData: LicenseData = JSON.parse(decoded);
        
        // Validate the structure
        if (!licenseData.email || !licenseData.expiresAt || !licenseData.activationExpiresAt || !licenseData.features) {
          return null;
        }

        return licenseData;
      } catch (innerError) {
        console.error('Failed to parse base64 license key:', innerError);
        return null;
      }
    } catch (error) {
      console.error('Failed to decrypt license key:', error);
      return null;
    }
  }

  private isDemoKey(key: string): boolean {
    // Check for demo key patterns or if it contains demo license data
    try {
      const decoded = atob(key.replace(/A+$/, '')); // Remove padding
      const data = JSON.parse(decoded);
      return data.email === '<EMAIL>' || data.isDemoLicense === true;
    } catch {
      return false;
    }
  }

  private generateDemoLicenseData(key: string): LicenseData {
    const now = new Date();
    // For demo licenses, both expiry and activation expiry should be 36 hours
    const activationExpiry = new Date(now.getTime() + (36 * 60 * 60 * 1000)); // 36 hours from now
    
    return {
      email: '<EMAIL>',
      expiresAt: activationExpiry.toISOString(), // Demo license expires when activation window ends
      activationExpiresAt: activationExpiry.toISOString(),
      features: ['unlimited_data', 'forecast_access', 'extended_tracking', 'full_history'],
      issuedAt: now.toISOString(),
      version: '1.0',
      isDemoLicense: true
    };
  }

  generateDemoLicenseKey(email: string): string {
    // Generate a demo license key for testing
    const now = new Date();
    const activationExpiry = new Date(now.getTime() + 36 * 60 * 60 * 1000); // 36 hours
    
    const licenseData: LicenseData = {
      email: email,
      expiresAt: activationExpiry.toISOString(), // Demo license expires with activation window
      activationExpiresAt: activationExpiry.toISOString(),
      features: ['unlimited_data', 'forecast_access', 'extended_tracking', 'full_history'],
      issuedAt: now.toISOString(),
      version: '1.0',
      isDemoLicense: true
    };

    // Encode as base64 (in production, this would be properly encrypted)
    const encoded = btoa(JSON.stringify(licenseData));
    
    // Pad to make it look more realistic (256+ characters)
    const padding = 'A'.repeat(Math.max(0, 256 - encoded.length));
    
    return encoded + padding;
  }

  getLicenseInfo(): LicenseInfo {
    return this.licenseInfo || this.getDefaultLicenseInfo();
  }

  isLicensed(): boolean {
    const license = this.getLicenseInfo();
    if (!license.isLicensed) return false;
    
    // Check if license is expired
    if (license.expiresAt && new Date(license.expiresAt) < new Date()) {
      return false;
    }
    
    return true;
  }

  canAccessFeature(feature: keyof LicenseInfo['features']): boolean {
    if (!this.isLicensed()) return false;
    return this.getLicenseInfo().features[feature];
  }

  // Feature-specific checks
  canUploadUnlimitedData(): boolean {
    return this.canAccessFeature('unlimitedData');
  }

  canAccessForecast(): boolean {
    return this.canAccessFeature('forecastAccess');
  }

  canAccessExtendedTracking(): boolean {
    return this.canAccessFeature('extendedTracking');
  }

  canAccessFullHistory(): boolean {
    return this.canAccessFeature('fullHistory');
  }

  // Data limitation checks
  getMaxDataMonths(): number {
    return this.isLicensed() ? Infinity : 3;
  }

  getMaxTrackingMonths(): number {
    return this.isLicensed() ? Infinity : 1;
  }

  getMaxHistoryMonths(): number {
    return this.isLicensed() ? Infinity : 3;
  }

  // Check if data exceeds limits
  isDataWithinLimits(transactions: any[]): { withinLimits: boolean; message?: string } {
    if (this.isLicensed()) {
      return { withinLimits: true };
    }

    const maxMonths = this.getMaxDataMonths();
    const cutoffDate = new Date();
    cutoffDate.setMonth(cutoffDate.getMonth() - maxMonths);

    const oldTransactions = transactions.filter(t => new Date(t.date) < cutoffDate);
    
    if (oldTransactions.length > 0) {
      return {
        withinLimits: false,
        message: `Unlicensed version limited to ${maxMonths} months of data. ${oldTransactions.length} transactions exceed this limit.`
      };
    }

    return { withinLimits: true };
  }

  // Get remaining time in activation window for demo licenses
  getDemoActivationTimeRemaining(): { hours: number; expired: boolean } | null {
    if (!this.licenseInfo || !this.licenseInfo.isLicensed || !this.licenseInfo.licenseKey) {
      return null;
    }

    try {
      if (this.isDemoKey(this.licenseInfo.licenseKey)) {
        const licenseData = this.generateDemoLicenseData(this.licenseInfo.licenseKey);
        const now = new Date();
        const activationExpiry = new Date(licenseData.activationExpiresAt);
        
        if (now > activationExpiry) {
          return { hours: 0, expired: true };
        }
        
        const hoursRemaining = Math.ceil((activationExpiry.getTime() - now.getTime()) / (1000 * 60 * 60));
        return { hours: hoursRemaining, expired: false };
      }
    } catch (error) {
      console.error('Error checking demo activation time:', error);
    }
    
    return null;
  }

  getLicenseStatusMessage(): string {
    const license = this.getLicenseInfo();
    
    if (!license.isLicensed) {
      return 'Unlicensed - Limited to 3 months of data, no forecast access, and 1 month tracking';
    }
    
    // Check for demo license activation window
    const demoTime = this.getDemoActivationTimeRemaining();
    if (demoTime) {
      if (demoTime.expired) {
        return 'Demo license expired - Data will be cleared on next access';
      } else if (demoTime.hours <= 24) {
        return `Demo license - ${demoTime.hours} hours remaining before data clearing`;
      } else {
        return `Demo license active - ${Math.ceil(demoTime.hours / 24)} days until data clearing`;
      }
    }
    
    if (license.expiresAt && new Date(license.expiresAt) < new Date()) {
      return 'License expired - Please renew to continue using full features';
    }
    
    if (license.expiresAt) {
      const daysUntilExpiry = Math.ceil(
        (new Date(license.expiresAt).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
      );
      
      if (daysUntilExpiry <= 30) {
        return `License expires in ${daysUntilExpiry} days`;
      }
    }
    
    return 'Licensed - Full access to all features';
  }

  getLicenseStatusColor(): string {
    const license = this.getLicenseInfo();
    
    if (!license.isLicensed) return 'text-red-400';
    
    // Check for demo license activation window
    const demoTime = this.getDemoActivationTimeRemaining();
    if (demoTime) {
      if (demoTime.expired) return 'text-red-400';
      if (demoTime.hours <= 12) return 'text-red-400';
      if (demoTime.hours <= 24) return 'text-yellow-400';
      return 'text-orange-400';
    }
    
    if (license.expiresAt && new Date(license.expiresAt) < new Date()) {
      return 'text-red-400';
    }
    
    if (license.expiresAt) {
      const daysUntilExpiry = Math.ceil(
        (new Date(license.expiresAt).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
      );
      
      if (daysUntilExpiry <= 30) return 'text-yellow-400';
    }
    
    return 'text-green-400';
  }
}

export const licenseManager = LicenseManager.getInstance();