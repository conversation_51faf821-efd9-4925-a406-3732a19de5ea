import { Transaction, BudgetCategory, IncomeProjection } from '../types';

export function calculateIncomeProjections(
  transactions: Transaction[], 
  incomeCategories: BudgetCategory[]
): Record<string, IncomeProjection> {
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();
  const currentDay = currentDate.getDate();
  
  // Get days in current month
  const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
  
  // Filter transactions for current month and income only
  const currentMonthIncomeTransactions = transactions.filter(t => {
    const transactionDate = new Date(t.date);
    return transactionDate.getMonth() === currentMonth &&
           transactionDate.getFullYear() === currentYear &&
           t.amount > 0; // Income transactions are positive
  });

  const projections: Record<string, IncomeProjection> = {};

  // Calculate projections for each income category
  incomeCategories.forEach(category => {
    const categoryTransactions = currentMonthIncomeTransactions.filter(
      t => t.category === category.name
    );
    
    const actualToDate = categoryTransactions.reduce((sum, t) => sum + t.amount, 0);
    const dailyRate = actualToDate / currentDay;
    const monthlyProjected = dailyRate * daysInMonth;
    const annualProjected = monthlyProjected * 12;

    projections[category.name] = {
      dailyRate,
      monthlyProjected,
      annualProjected,
      daysInMonth,
      daysElapsed: currentDay,
      actualToDate
    };
  });

  return projections;
}

export function calculateOverallIncomeProjection(
  transactions: Transaction[]
): IncomeProjection {
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();
  const currentDay = currentDate.getDate();
  
  const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
  
  const currentMonthIncome = transactions
    .filter(t => {
      const transactionDate = new Date(t.date);
      return transactionDate.getMonth() === currentMonth &&
             transactionDate.getFullYear() === currentYear &&
             t.amount > 0;
    })
    .reduce((sum, t) => sum + t.amount, 0);

  const dailyRate = currentMonthIncome / currentDay;
  const monthlyProjected = dailyRate * daysInMonth;
  const annualProjected = monthlyProjected * 12;

  return {
    dailyRate,
    monthlyProjected,
    annualProjected,
    daysInMonth,
    daysElapsed: currentDay,
    actualToDate: currentMonthIncome
  };
}