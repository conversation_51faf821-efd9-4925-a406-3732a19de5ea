import { Transaction, MonthlyHistory } from '../types';

export interface ProcessedTransactionResult {
  transactions: Transaction[];
  monthlyHistories: MonthlyHistory[];
  internalTransfers: Transaction[];
  duplicatesSkipped: number;
}

export interface TransferPair {
  outgoing: Transaction;
  incoming: Transaction;
  confidence: number;
}

/**
 * Enhanced transaction processor that handles multi-month data and internal transfers
 */
export class TransactionProcessor {
  
  /**
   * Process transactions with multi-month organization and transfer detection
   */
  static async processTransactions(
    newTransactions: Transaction[], 
    existingTransactions: Transaction[] = []
  ): Promise<ProcessedTransactionResult> {
    
    // Step 1: Remove duplicates
    const { uniqueTransactions, duplicatesSkipped } = this.removeDuplicates(newTransactions, existingTransactions);
    
    // Step 2: Detect and handle internal transfers
    const { cleanTransactions, internalTransfers } = this.detectInternalTransfers(uniqueTransactions);
    
    // Step 3: Organize transactions by month
    const monthlyHistories = this.organizeByMonth(cleanTransactions);
    
    return {
      transactions: cleanTransactions,
      monthlyHistories,
      internalTransfers,
      duplicatesSkipped
    };
  }

  /**
   * Remove duplicate transactions based on date, description, and amount
   */
  private static removeDuplicates(
    newTransactions: Transaction[], 
    existingTransactions: Transaction[]
  ): { uniqueTransactions: Transaction[]; duplicatesSkipped: number } {
    
    const existingSet = new Set(
      existingTransactions.map(t => `${t.date}-${t.description.trim()}-${t.amount}`)
    );
    
    const uniqueTransactions = newTransactions.filter(t => 
      !existingSet.has(`${t.date}-${t.description.trim()}-${t.amount}`)
    );
    
    return {
      uniqueTransactions,
      duplicatesSkipped: newTransactions.length - uniqueTransactions.length
    };
  }

  /**
   * Detect internal transfers between accounts
   */
  private static detectInternalTransfers(
    transactions: Transaction[]
  ): { cleanTransactions: Transaction[]; internalTransfers: Transaction[] } {
    
    const transferPairs: TransferPair[] = [];
    const internalTransfers: Transaction[] = [];
    const cleanTransactions: Transaction[] = [];
    
    // Group transactions by date for efficient matching
    const transactionsByDate = new Map<string, Transaction[]>();
    
    transactions.forEach(transaction => {
      const date = transaction.date;
      if (!transactionsByDate.has(date)) {
        transactionsByDate.set(date, []);
      }
      transactionsByDate.get(date)!.push(transaction);
    });
    
    const processedIds = new Set<string>();
    
    // Look for transfer pairs within each date group
    transactionsByDate.forEach((dayTransactions, date) => {
      for (let i = 0; i < dayTransactions.length; i++) {
        const transaction1 = dayTransactions[i];
        
        if (processedIds.has(transaction1.id)) continue;
        
        for (let j = i + 1; j < dayTransactions.length; j++) {
          const transaction2 = dayTransactions[j];
          
          if (processedIds.has(transaction2.id)) continue;
          
          const transferPair = this.isTransferPair(transaction1, transaction2);
          if (transferPair) {
            transferPairs.push(transferPair);
            internalTransfers.push(transferPair.outgoing, transferPair.incoming);
            processedIds.add(transferPair.outgoing.id);
            processedIds.add(transferPair.incoming.id);
            break;
          }
        }
      }
    });
    
    // Add non-transfer transactions to clean list
    transactions.forEach(transaction => {
      if (!processedIds.has(transaction.id)) {
        cleanTransactions.push(transaction);
      }
    });
    
    return { cleanTransactions, internalTransfers };
  }

  /**
   * Check if two transactions form a transfer pair
   */
  private static isTransferPair(t1: Transaction, t2: Transaction): TransferPair | null {
    // Must be on the same date
    if (t1.date !== t2.date) return null;
    
    // Must have opposite amounts (one positive, one negative)
    if (Math.sign(t1.amount) === Math.sign(t2.amount)) return null;
    
    // Amounts must be equal in absolute value (within small tolerance for rounding)
    const amount1 = Math.abs(t1.amount);
    const amount2 = Math.abs(t2.amount);
    const tolerance = 0.01; // 1 cent tolerance
    
    if (Math.abs(amount1 - amount2) > tolerance) return null;
    
    let confidence = 0.5; // Base confidence for same date and amount
    
    // Check for transfer indicators in descriptions
    const transferKeywords = [
      'transfer', 'tfr', 'xfer', 'internal', 'between accounts',
      'from', 'to', 'account', 'savings', 'checking', 'credit card'
    ];
    
    const desc1 = t1.description.toLowerCase();
    const desc2 = t2.description.toLowerCase();
    
    // Boost confidence if descriptions contain transfer keywords
    transferKeywords.forEach(keyword => {
      if (desc1.includes(keyword) || desc2.includes(keyword)) {
        confidence += 0.1;
      }
    });
    
    // Boost confidence if descriptions are similar
    if (this.calculateSimilarity(desc1, desc2) > 0.3) {
      confidence += 0.2;
    }
    
    // Boost confidence if one description mentions the other account
    if (desc1.includes('savings') && desc2.includes('checking') ||
        desc1.includes('checking') && desc2.includes('savings') ||
        desc1.includes('credit') && desc2.includes('checking') ||
        desc1.includes('checking') && desc2.includes('credit')) {
      confidence += 0.3;
    }
    
    // Only consider it a transfer if confidence is above threshold
    if (confidence < 0.7) return null;
    
    // Determine which is outgoing (negative) and incoming (positive)
    const outgoing = t1.amount < 0 ? t1 : t2;
    const incoming = t1.amount > 0 ? t1 : t2;
    
    return { outgoing, incoming, confidence };
  }

  /**
   * Calculate similarity between two strings
   */
  private static calculateSimilarity(str1: string, str2: string): number {
    const words1 = str1.split(/\s+/);
    const words2 = str2.split(/\s+/);
    
    let commonWords = 0;
    const totalWords = Math.max(words1.length, words2.length);
    
    words1.forEach(word => {
      if (words2.includes(word) && word.length > 2) {
        commonWords++;
      }
    });
    
    return commonWords / totalWords;
  }

  /**
   * Organize transactions by month and create monthly history records
   */
  private static organizeByMonth(transactions: Transaction[]): MonthlyHistory[] {
    const monthlyData = new Map<string, {
      month: string;
      year: number;
      transactions: Transaction[];
      totalSpent: number;
      totalIncome: number;
      categories: Record<string, number>;
      incomeCategories: Record<string, number>;
    }>();

    transactions.forEach(transaction => {
      const date = new Date(transaction.date);
      const year = date.getFullYear();
      const month = date.toLocaleString('default', { month: 'long' });
      const monthKey = `${year}-${month}`;

      if (!monthlyData.has(monthKey)) {
        monthlyData.set(monthKey, {
          month,
          year,
          transactions: [],
          totalSpent: 0,
          totalIncome: 0,
          categories: {},
          incomeCategories: {}
        });
      }

      const monthData = monthlyData.get(monthKey)!;
      monthData.transactions.push(transaction);

      if (transaction.amount < 0) {
        // Expense
        monthData.totalSpent += Math.abs(transaction.amount);
        monthData.categories[transaction.category] = 
          (monthData.categories[transaction.category] || 0) + Math.abs(transaction.amount);
      } else {
        // Income
        monthData.totalIncome += transaction.amount;
        monthData.incomeCategories[transaction.category] = 
          (monthData.incomeCategories[transaction.category] || 0) + transaction.amount;
      }
    });

    // Convert to MonthlyHistory array
    return Array.from(monthlyData.values()).map(data => ({
      ...data,
      netIncome: data.totalIncome - data.totalSpent
    }));
  }

  /**
   * Merge new monthly histories with existing ones
   */
  static mergeMonthlyHistories(
    newHistories: MonthlyHistory[], 
    existingHistories: MonthlyHistory[]
  ): MonthlyHistory[] {
    const historyMap = new Map<string, MonthlyHistory>();

    // Add existing histories
    existingHistories.forEach(history => {
      const key = `${history.year}-${history.month}`;
      historyMap.set(key, history);
    });

    // Merge or add new histories
    newHistories.forEach(newHistory => {
      const key = `${newHistory.year}-${newHistory.month}`;
      const existing = historyMap.get(key);

      if (existing) {
        // Merge with existing
        const mergedTransactions = [...existing.transactions, ...newHistory.transactions];
        const mergedCategories = { ...existing.categories };
        const mergedIncomeCategories = { ...existing.incomeCategories };

        // Merge category totals
        Object.entries(newHistory.categories).forEach(([category, amount]) => {
          mergedCategories[category] = (mergedCategories[category] || 0) + amount;
        });

        Object.entries(newHistory.incomeCategories).forEach(([category, amount]) => {
          mergedIncomeCategories[category] = (mergedIncomeCategories[category] || 0) + amount;
        });

        historyMap.set(key, {
          month: newHistory.month,
          year: newHistory.year,
          transactions: mergedTransactions,
          totalSpent: existing.totalSpent + newHistory.totalSpent,
          totalIncome: existing.totalIncome + newHistory.totalIncome,
          netIncome: (existing.totalIncome + newHistory.totalIncome) - (existing.totalSpent + newHistory.totalSpent),
          categories: mergedCategories,
          incomeCategories: mergedIncomeCategories
        });
      } else {
        // Add new history
        historyMap.set(key, newHistory);
      }
    });

    return Array.from(historyMap.values()).sort((a, b) => {
      if (a.year !== b.year) return b.year - a.year;
      return new Date(`${a.month} 1, ${a.year}`).getMonth() - new Date(`${b.month} 1, ${b.year}`).getMonth();
    });
  }
}
