/**
 * Environment variable utility for the BudgetPage application
 * Handles loading environment variables from .env file or from server
 */

// Cache for environment variables
let envCache: Record<string, string | undefined> = {};

export const env = {
  /**
   * Gets an environment variable
   * @param key - The environment variable name to get
   * @param defaultValue - Default value if not found
   * @returns The environment variable value or default
   */
  get(key: string, defaultValue: string = ''): string {
    // Return from cache first if available
    if (envCache[key] !== undefined) {
      return envCache[key] || defaultValue;
    }
    
    // Check if vite exposes it via import.meta.env
    const viteEnv = (import.meta.env as Record<string, any>)[key] || 
                   (import.meta.env as Record<string, any>)[`VITE_${key}`];
    
    if (viteEnv) {
      envCache[key] = viteEnv;
      return viteEnv;
    }
    
    // Otherwise return the default
    return defaultValue;
  },

  /**
   * Set the environment values in cache
   * @param values - Key-value pairs of environment variables
   */
  setAll(values: Record<string, string>): void {
    envCache = { ...envCache, ...values };
  },

  /**
   * Loads environment variables from the server
   * @returns Promise that resolves when environment variables are loaded
   */
  async loadFromServer(): Promise<boolean> {
    try {
      const response = await fetch('/api/env');
      if (response.ok) {
        const data = await response.json();
        this.setAll(data);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to load environment variables from server:', error);
      return false;
    }
  }
};

// Export common environment variables
export const OPENAI_API_KEY = env.get('OPENAI_API_KEY', '');
