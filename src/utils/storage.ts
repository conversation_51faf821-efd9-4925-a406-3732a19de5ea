import { Transaction, BudgetCategory, MonthlyHistory, KeywordMapping, TrackedExpense, LicenseInfo } from '../types';
import { indexedDBStorage } from './indexedDBStorage';

const STORAGE_KEYS = {
  TRANSACTIONS: 'budget_transactions',
  BUDGET: 'budget_categories',
  HISTORY: 'budget_history',
  SETTINGS: 'budget_settings',
  KEYWORDS: 'budget_keywords',
  TRACKED_EXPENSES: 'budget_tracked_expenses',
  LICENSE: 'budget_license'
};

// Migration helper to move data from localStorage to IndexedDB
async function migrateFromLocalStorage(): Promise<void> {
  try {
    // Check if migration is needed
    const migrationKey = 'budget_migrated_to_indexeddb';
    if (localStorage.getItem(migrationKey)) {
      return; // Already migrated
    }

    console.log('Starting migration from localStorage to IndexedDB...');

    // Migrate transactions
    const transactionsData = localStorage.getItem(STORAGE_KEYS.TRANSACTIONS);
    if (transactionsData) {
      const transactions: Transaction[] = JSON.parse(transactionsData);
      await indexedDBStorage.putAll('transactions', transactions);
    }

    // Migrate budget categories
    const budgetData = localStorage.getItem(STORAGE_KEYS.BUDGET);
    if (budgetData) {
      const categories: BudgetCategory[] = JSON.parse(budgetData);
      // Add type field to existing categories during migration
      const updatedCategories = categories.map(cat => ({
        ...cat,
        type: cat.type || 'expense' as 'expense' | 'income'
      }));
      await indexedDBStorage.putAll('budgetCategories', updatedCategories);
    }

    // Migrate history
    const historyData = localStorage.getItem(STORAGE_KEYS.HISTORY);
    if (historyData) {
      const history: MonthlyHistory[] = JSON.parse(historyData);
      // Add income fields to existing history during migration
      const updatedHistory = history.map(h => ({
        ...h,
        totalIncome: h.totalIncome || 0,
        netIncome: h.netIncome || (h.totalIncome || 0) - h.totalSpent,
        incomeCategories: h.incomeCategories || {}
      }));
      await indexedDBStorage.putAll('monthlyHistory', updatedHistory);
    }

    // Migrate keywords
    const keywordsData = localStorage.getItem(STORAGE_KEYS.KEYWORDS);
    if (keywordsData) {
      const keywords: KeywordMapping[] = JSON.parse(keywordsData);
      await indexedDBStorage.putAll('keywordMappings', keywords);
    }

    // Migrate tracked expenses
    const trackedData = localStorage.getItem(STORAGE_KEYS.TRACKED_EXPENSES);
    if (trackedData) {
      const tracked: TrackedExpense[] = JSON.parse(trackedData);
      await indexedDBStorage.putAll('trackedExpenses', tracked);
    }

    // Migrate settings
    const settingsData = localStorage.getItem(STORAGE_KEYS.SETTINGS);
    if (settingsData) {
      const settings = JSON.parse(settingsData);
      await indexedDBStorage.put('settings', { id: 'main', ...settings });
    }

    // Migrate license info
    const licenseData = localStorage.getItem(STORAGE_KEYS.LICENSE);
    if (licenseData) {
      const license = JSON.parse(licenseData);
      await indexedDBStorage.put('license', { id: 'main', ...license });
    }

    // Mark migration as complete
    localStorage.setItem(migrationKey, 'true');
    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
    // Continue with localStorage as fallback
  }
}

export const storage = {
  // Initialize storage and perform migration
  async init(): Promise<void> {
    try {
      await indexedDBStorage.init();
      await migrateFromLocalStorage();
    } catch (error) {
      console.error('Storage initialization failed:', error);
      // Continue with localStorage fallback
    }
  },

  // Transactions
  async getTransactions(): Promise<Transaction[]> {
    try {
      const transactions = await indexedDBStorage.getAll<Transaction>('transactions');
      return transactions || [];
    } catch (error) {
      console.error('Failed to get transactions from IndexedDB, falling back to localStorage:', error);
      const data = localStorage.getItem(STORAGE_KEYS.TRANSACTIONS);
      return data ? JSON.parse(data) : [];
    }
  },

  async saveTransactions(transactions: Transaction[]): Promise<void> {
    try {
      await indexedDBStorage.clear('transactions');
      await indexedDBStorage.putAll('transactions', transactions);
      // Keep localStorage as backup
      localStorage.setItem(STORAGE_KEYS.TRANSACTIONS, JSON.stringify(transactions));
    } catch (error) {
      console.error('Failed to save transactions to IndexedDB, using localStorage:', error);
      localStorage.setItem(STORAGE_KEYS.TRANSACTIONS, JSON.stringify(transactions));
    }
  },

  async addTransactions(newTransactions: Transaction[]): Promise<void> {
    const existing = await this.getTransactions();
    
    // Check for duplicates based on date, description, and amount
    const existingSet = new Set(
      existing.map(t => `${t.date}-${t.description}-${t.amount}`)
    );
    
    const uniqueTransactions = newTransactions.filter(t => 
      !existingSet.has(`${t.date}-${t.description}-${t.amount}`)
    );
    
    if (uniqueTransactions.length > 0) {
      const combined = [...existing, ...uniqueTransactions];
      await this.saveTransactions(combined);
    }
    
    return {
      added: uniqueTransactions.length,
      skipped: newTransactions.length - uniqueTransactions.length,
      total: newTransactions.length
    } as any;
  },

  // Budget Categories
  async getBudgetCategories(): Promise<BudgetCategory[]> {
    try {
      const categories = await indexedDBStorage.getAll<BudgetCategory>('budgetCategories');
      return categories.length > 0 ? categories : this.getDefaultBudgetCategories();
    } catch (error) {
      console.error('Failed to get budget categories from IndexedDB, falling back to localStorage:', error);
      const data = localStorage.getItem(STORAGE_KEYS.BUDGET);
      return data ? JSON.parse(data) : this.getDefaultBudgetCategories();
    }
  },

  async saveBudgetCategories(categories: BudgetCategory[]): Promise<void> {
    try {
      await indexedDBStorage.clear('budgetCategories');
      await indexedDBStorage.putAll('budgetCategories', categories);
      localStorage.setItem(STORAGE_KEYS.BUDGET, JSON.stringify(categories));
    } catch (error) {
      console.error('Failed to save budget categories to IndexedDB, using localStorage:', error);
      localStorage.setItem(STORAGE_KEYS.BUDGET, JSON.stringify(categories));
    }
  },

  getDefaultBudgetCategories(): BudgetCategory[] {
    return [
      // Expense categories
      { id: '1', name: 'Housing', budgetAmount: 1500, currentSpending: 0, type: 'expense' },
      { id: '2', name: 'Transportation', budgetAmount: 400, currentSpending: 0, type: 'expense' },
      { id: '3', name: 'Food', budgetAmount: 600, currentSpending: 0, type: 'expense' },
      { id: '4', name: 'Utilities', budgetAmount: 200, currentSpending: 0, type: 'expense' },
      { id: '5', name: 'Insurance', budgetAmount: 300, currentSpending: 0, type: 'expense' },
      { id: '6', name: 'Healthcare', budgetAmount: 200, currentSpending: 0, type: 'expense' },
      { id: '7', name: 'Entertainment', budgetAmount: 300, currentSpending: 0, type: 'expense' },
      { id: '8', name: 'Shopping', budgetAmount: 250, currentSpending: 0, type: 'expense' },
      { id: '9', name: 'Personal Care', budgetAmount: 100, currentSpending: 0, type: 'expense' },
      { id: '10', name: 'Miscellaneous', budgetAmount: 150, currentSpending: 0, type: 'expense' },
      // Income categories
      { id: '11', name: 'Salary', budgetAmount: 5000, currentSpending: 0, type: 'income' },
      { id: '12', name: 'Freelance', budgetAmount: 1000, currentSpending: 0, type: 'income' },
      { id: '13', name: 'Investments', budgetAmount: 200, currentSpending: 0, type: 'income' },
      { id: '14', name: 'Other Income', budgetAmount: 100, currentSpending: 0, type: 'income' }
    ];
  },

  async addBudgetCategory(name: string, type: 'expense' | 'income' = 'expense'): Promise<BudgetCategory> {
    const categories = await this.getBudgetCategories();
    const newCategory: BudgetCategory = {
      id: Date.now().toString(),
      name,
      budgetAmount: 0,
      currentSpending: 0,
      type
    };
    categories.push(newCategory);
    await this.saveBudgetCategories(categories);
    return newCategory;
  },

  async removeBudgetCategory(categoryId: string): Promise<void> {
    const categories = await this.getBudgetCategories();
    const filtered = categories.filter(cat => cat.id !== categoryId);
    await this.saveBudgetCategories(filtered);
    
    // Update transactions that used this category to "Miscellaneous"
    const transactions = await this.getTransactions();
    const categoryToRemove = categories.find(cat => cat.id === categoryId);
    if (categoryToRemove) {
      const updatedTransactions = transactions.map(transaction => 
        transaction.category === categoryToRemove.name 
          ? { ...transaction, category: 'Miscellaneous' }
          : transaction
      );
      await this.saveTransactions(updatedTransactions);

      // Remove keyword mappings for this category
      const keywords = await this.getKeywordMappings();
      const filteredKeywords = keywords.filter(kw => kw.categoryName !== categoryToRemove.name);
      await this.saveKeywordMappings(filteredKeywords);
    }
  },

  async updateBudgetCategoryName(categoryId: string, newName: string): Promise<void> {
    const categories = await this.getBudgetCategories();
    const categoryIndex = categories.findIndex(cat => cat.id === categoryId);
    
    if (categoryIndex >= 0) {
      const oldName = categories[categoryIndex].name;
      categories[categoryIndex].name = newName;
      await this.saveBudgetCategories(categories);
      
      // Update all transactions that used the old category name
      const transactions = await this.getTransactions();
      const updatedTransactions = transactions.map(transaction => 
        transaction.category === oldName 
          ? { ...transaction, category: newName }
          : transaction
      );
      await this.saveTransactions(updatedTransactions);
      
      // Update history records
      const history = await this.getHistory();
      const updatedHistory = history.map(monthData => ({
        ...monthData,
        transactions: monthData.transactions.map(transaction =>
          transaction.category === oldName
            ? { ...transaction, category: newName }
            : transaction
        ),
        categories: Object.keys(monthData.categories).reduce((acc, key) => {
          const newKey = key === oldName ? newName : key;
          acc[newKey] = monthData.categories[key];
          return acc;
        }, {} as Record<string, number>),
        incomeCategories: Object.keys(monthData.incomeCategories || {}).reduce((acc, key) => {
          const newKey = key === oldName ? newName : key;
          acc[newKey] = (monthData.incomeCategories || {})[key];
          return acc;
        }, {} as Record<string, number>)
      }));
      await this.saveHistory(updatedHistory);

      // Update keyword mappings
      const keywords = await this.getKeywordMappings();
      const updatedKeywords = keywords.map(kw => 
        kw.categoryName === oldName 
          ? { ...kw, categoryName: newName }
          : kw
      );
      await this.saveKeywordMappings(updatedKeywords);
    }
  },

  async getCategoryNames(): Promise<string[]> {
    const categories = await this.getBudgetCategories();
    return categories.map(cat => cat.name);
  },

  // Keyword Mappings
  async getKeywordMappings(): Promise<KeywordMapping[]> {
    try {
      const keywords = await indexedDBStorage.getAll<KeywordMapping>('keywordMappings');
      return keywords || [];
    } catch (error) {
      console.error('Failed to get keyword mappings from IndexedDB, falling back to localStorage:', error);
      const data = localStorage.getItem(STORAGE_KEYS.KEYWORDS);
      return data ? JSON.parse(data) : [];
    }
  },

  async saveKeywordMappings(keywords: KeywordMapping[]): Promise<void> {
    try {
      await indexedDBStorage.clear('keywordMappings');
      await indexedDBStorage.putAll('keywordMappings', keywords);
      localStorage.setItem(STORAGE_KEYS.KEYWORDS, JSON.stringify(keywords));
    } catch (error) {
      console.error('Failed to save keyword mappings to IndexedDB, using localStorage:', error);
      localStorage.setItem(STORAGE_KEYS.KEYWORDS, JSON.stringify(keywords));
    }
  },

  async addKeywordMapping(keyword: string, categoryName: string): Promise<KeywordMapping> {
    const keywords = await this.getKeywordMappings();
    const newMapping: KeywordMapping = {
      id: Date.now().toString(),
      keyword: keyword.toLowerCase().trim(),
      categoryName,
      createdAt: new Date().toISOString()
    };
    keywords.push(newMapping);
    await this.saveKeywordMappings(keywords);
    return newMapping;
  },

  async removeKeywordMapping(keywordId: string): Promise<void> {
    const keywords = await this.getKeywordMappings();
    const filtered = keywords.filter(kw => kw.id !== keywordId);
    await this.saveKeywordMappings(filtered);
  },

  async updateKeywordMapping(keywordId: string, keyword: string, categoryName: string): Promise<void> {
    const keywords = await this.getKeywordMappings();
    const keywordIndex = keywords.findIndex(kw => kw.id === keywordId);
    
    if (keywordIndex >= 0) {
      keywords[keywordIndex] = {
        ...keywords[keywordIndex],
        keyword: keyword.toLowerCase().trim(),
        categoryName
      };
      await this.saveKeywordMappings(keywords);
    }
  },

  // Tracked Expenses
  async getTrackedExpenses(): Promise<TrackedExpense[]> {
    try {
      const tracked = await indexedDBStorage.getAll<TrackedExpense>('trackedExpenses');
      return tracked || [];
    } catch (error) {
      console.error('Failed to get tracked expenses from IndexedDB, falling back to localStorage:', error);
      const data = localStorage.getItem(STORAGE_KEYS.TRACKED_EXPENSES);
      return data ? JSON.parse(data) : [];
    }
  },

  async saveTrackedExpenses(tracked: TrackedExpense[]): Promise<void> {
    try {
      await indexedDBStorage.clear('trackedExpenses');
      await indexedDBStorage.putAll('trackedExpenses', tracked);
      localStorage.setItem(STORAGE_KEYS.TRACKED_EXPENSES, JSON.stringify(tracked));
    } catch (error) {
      console.error('Failed to save tracked expenses to IndexedDB, using localStorage:', error);
      localStorage.setItem(STORAGE_KEYS.TRACKED_EXPENSES, JSON.stringify(tracked));
    }
  },

  async addTrackedExpense(expense: Omit<TrackedExpense, 'id' | 'createdAt' | 'updatedAt'>): Promise<TrackedExpense> {
    const tracked = await this.getTrackedExpenses();
    const newExpense: TrackedExpense = {
      ...expense,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    tracked.push(newExpense);
    await this.saveTrackedExpenses(tracked);
    return newExpense;
  },

  async updateTrackedExpense(expenseId: string, updates: Partial<TrackedExpense>): Promise<void> {
    const tracked = await this.getTrackedExpenses();
    const index = tracked.findIndex(t => t.id === expenseId);
    
    if (index >= 0) {
      tracked[index] = {
        ...tracked[index],
        ...updates,
        updatedAt: new Date().toISOString()
      };
      await this.saveTrackedExpenses(tracked);
    }
  },

  async removeTrackedExpense(expenseId: string): Promise<void> {
    const tracked = await this.getTrackedExpenses();
    const filtered = tracked.filter(t => t.id !== expenseId);
    await this.saveTrackedExpenses(filtered);
  },

  // History
  async getHistory(): Promise<MonthlyHistory[]> {
    try {
      const history = await indexedDBStorage.getAll<MonthlyHistory>('monthlyHistory');
      return history || [];
    } catch (error) {
      console.error('Failed to get history from IndexedDB, falling back to localStorage:', error);
      const data = localStorage.getItem(STORAGE_KEYS.HISTORY);
      return data ? JSON.parse(data) : [];
    }
  },

  async saveHistory(history: MonthlyHistory[]): Promise<void> {
    try {
      await indexedDBStorage.clear('monthlyHistory');
      await indexedDBStorage.putAll('monthlyHistory', history);
      localStorage.setItem(STORAGE_KEYS.HISTORY, JSON.stringify(history));
    } catch (error) {
      console.error('Failed to save history to IndexedDB, using localStorage:', error);
      localStorage.setItem(STORAGE_KEYS.HISTORY, JSON.stringify(history));
    }
  },

  async addMonthlyHistory(monthData: MonthlyHistory): Promise<void> {
    const history = await this.getHistory();
    const existingIndex = history.findIndex(
      h => h.month === monthData.month && h.year === monthData.year
    );
    
    if (existingIndex >= 0) {
      history[existingIndex] = monthData;
    } else {
      history.push(monthData);
    }
    
    await this.saveHistory(history);
  },

  // License Management
  async getLicenseInfo(): Promise<LicenseInfo> {
    try {
      const license = await indexedDBStorage.get<LicenseInfo & { id: string }>('license', 'main');
      if (license) {
        const { id, ...licenseInfo } = license;
        return licenseInfo;
      }
    } catch (error) {
      console.error('Failed to get license from IndexedDB, falling back to localStorage:', error);
    }
    
    // Fallback to localStorage
    const data = localStorage.getItem(STORAGE_KEYS.LICENSE);
    if (data) {
      return JSON.parse(data);
    }
    
    // Return default unlicensed state
    return {
      isLicensed: false,
      email: '',
      licenseKey: '',
      features: {
        unlimitedData: false,
        forecastAccess: false,
        extendedTracking: false,
        fullHistory: false
      }
    };
  },

  async saveLicenseInfo(license: LicenseInfo): Promise<void> {
    try {
      await indexedDBStorage.put('license', { id: 'main', ...license });
      localStorage.setItem(STORAGE_KEYS.LICENSE, JSON.stringify(license));
    } catch (error) {
      console.error('Failed to save license to IndexedDB, using localStorage:', error);
      localStorage.setItem(STORAGE_KEYS.LICENSE, JSON.stringify(license));
    }
  },

  // Settings
  async getSettings(): Promise<{ darkMode: boolean; apiKey: string; aiModel: string }> {
    try {
      const settings = await indexedDBStorage.get<{ id: string; darkMode: boolean; apiKey: string }>('settings', 'main');
      return settings ? { darkMode: settings.darkMode, apiKey: settings.apiKey, aiModel: settings.aiModel || 'gpt-3.5-turbo-instruct' } : { darkMode: true, apiKey: '', aiModel: 'gpt-3.5-turbo-instruct' };
    } catch (error) {
      console.error('Failed to get settings from IndexedDB, falling back to localStorage:', error);
      const data = localStorage.getItem(STORAGE_KEYS.SETTINGS);
      return data ? JSON.parse(data) : { darkMode: true, apiKey: '', aiModel: 'gpt-3.5-turbo-instruct' };
    }
  },

  async saveSettings(settings: { darkMode: boolean; apiKey: string; aiModel: string }): Promise<void> {
    try {
      await indexedDBStorage.put('settings', { id: 'main', ...settings });
      localStorage.setItem(STORAGE_KEYS.SETTINGS, JSON.stringify(settings));
    } catch (error) {
      console.error('Failed to save settings to IndexedDB, using localStorage:', error);
      localStorage.setItem(STORAGE_KEYS.SETTINGS, JSON.stringify(settings));
    }
  },

  // Clear functions
  async clearTransactions(): Promise<void> {
    try {
      await indexedDBStorage.clear('transactions');
    } catch (error) {
      console.error('Failed to clear transactions from IndexedDB:', error);
    }
    localStorage.removeItem(STORAGE_KEYS.TRANSACTIONS);
  },

  async clearBudget(): Promise<void> {
    try {
      await indexedDBStorage.clear('budgetCategories');
    } catch (error) {
      console.error('Failed to clear budget from IndexedDB:', error);
    }
    localStorage.removeItem(STORAGE_KEYS.BUDGET);
  },

  async clearHistory(): Promise<void> {
    try {
      await indexedDBStorage.clear('monthlyHistory');
    } catch (error) {
      console.error('Failed to clear history from IndexedDB:', error);
    }
    localStorage.removeItem(STORAGE_KEYS.HISTORY);
  },

  async clearKeywords(): Promise<void> {
    try {
      await indexedDBStorage.clear('keywordMappings');
    } catch (error) {
      console.error('Failed to clear keywords from IndexedDB:', error);
    }
    localStorage.removeItem(STORAGE_KEYS.KEYWORDS);
  },

  async clearTrackedExpenses(): Promise<void> {
    try {
      await indexedDBStorage.clear('trackedExpenses');
    } catch (error) {
      console.error('Failed to clear tracked expenses from IndexedDB:', error);
    }
    localStorage.removeItem(STORAGE_KEYS.TRACKED_EXPENSES);
  },

  async clearLicense(): Promise<void> {
    try {
      await indexedDBStorage.delete('license', 'main');
    } catch (error) {
      console.error('Failed to clear license from IndexedDB:', error);
    }
    localStorage.removeItem(STORAGE_KEYS.LICENSE);
  },

  async clearAll(): Promise<void> {
    try {
      await indexedDBStorage.clearAll();
    } catch (error) {
      console.error('Failed to clear all data from IndexedDB:', error);
    }
    Object.values(STORAGE_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });
  },

  async clearMonthData(month: string, year: number): Promise<void> {
    const history = await this.getHistory();
    const filtered = history.filter(h => !(h.month === month && h.year === year));
    await this.saveHistory(filtered);
  }
};