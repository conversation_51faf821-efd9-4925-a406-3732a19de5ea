import { Transaction, BudgetCategory, MonthlyHistory, KeywordMapping, TrackedExpense, LicenseInfo } from '../types';
import { indexedDBStorage } from './indexedDBStorage';
import { fileStorage } from './fileStorage';

// Session-only storage keys (kept in browser localStorage)
const SESSION_STORAGE_KEYS = {
  DARK_MODE: 'budget_session_dark_mode',
  CURRENT_TAB: 'budget_session_current_tab'
};

// Legacy storage keys for migration
const LEGACY_STORAGE_KEYS = {
  TRANSACTIONS: 'budget_transactions',
  BUDGET: 'budget_categories',
  HISTORY: 'budget_history',
  SETTINGS: 'budget_settings',
  KEYWORDS: 'budget_keywords',
  TRACKED_EXPENSES: 'budget_tracked_expenses',
  LICENSE: 'budget_license'
};

// Migration helper to move data from browser storage to server storage
async function migrateToServerStorage(): Promise<void> {
  try {
    // Check if migration is needed
    const migrationKey = 'budget_migrated_to_server';
    if (localStorage.getItem(migrationKey)) {
      return; // Already migrated
    }

    console.log('Starting migration from browser storage to server...');

    // Initialize file storage
    const serverAvailable = await fileStorage.init();
    if (!serverAvailable) {
      console.warn('Server storage not available, skipping migration');
      return;
    }

    // Migrate transactions from localStorage or IndexedDB
    let transactionsData = localStorage.getItem(LEGACY_STORAGE_KEYS.TRANSACTIONS);
    if (!transactionsData) {
      try {
        const transactions = await indexedDBStorage.getAll<Transaction>('transactions');
        if (transactions && transactions.length > 0) {
          await fileStorage.saveData('transactions', transactions);
        }
      } catch (error) {
        console.warn('Failed to migrate transactions from IndexedDB:', error);
      }
    } else {
      const transactions: Transaction[] = JSON.parse(transactionsData);
      await fileStorage.saveData('transactions', transactions);
    }

    // Migrate budget categories
    let budgetData = localStorage.getItem(LEGACY_STORAGE_KEYS.BUDGET);
    if (!budgetData) {
      try {
        const categories = await indexedDBStorage.getAll<BudgetCategory>('budgetCategories');
        if (categories && categories.length > 0) {
          await fileStorage.saveData('budget', categories);
        }
      } catch (error) {
        console.warn('Failed to migrate budget from IndexedDB:', error);
      }
    } else {
      const categories: BudgetCategory[] = JSON.parse(budgetData);
      await fileStorage.saveData('budget', categories);
    }

    // Migrate history
    let historyData = localStorage.getItem(LEGACY_STORAGE_KEYS.HISTORY);
    if (!historyData) {
      try {
        const history = await indexedDBStorage.getAll<MonthlyHistory>('monthlyHistory');
        if (history && history.length > 0) {
          await fileStorage.saveData('history', history);
        }
      } catch (error) {
        console.warn('Failed to migrate history from IndexedDB:', error);
      }
    } else {
      const history: MonthlyHistory[] = JSON.parse(historyData);
      await fileStorage.saveData('history', history);
    }

    // Migrate keywords
    let keywordsData = localStorage.getItem(LEGACY_STORAGE_KEYS.KEYWORDS);
    if (!keywordsData) {
      try {
        const keywords = await indexedDBStorage.getAll<KeywordMapping>('keywordMappings');
        if (keywords && keywords.length > 0) {
          await fileStorage.saveData('keywords', keywords);
        }
      } catch (error) {
        console.warn('Failed to migrate keywords from IndexedDB:', error);
      }
    } else {
      const keywords: KeywordMapping[] = JSON.parse(keywordsData);
      await fileStorage.saveData('keywords', keywords);
    }

    // Migrate tracked expenses
    let trackedData = localStorage.getItem(LEGACY_STORAGE_KEYS.TRACKED_EXPENSES);
    if (!trackedData) {
      try {
        const tracked = await indexedDBStorage.getAll<TrackedExpense>('trackedExpenses');
        if (tracked && tracked.length > 0) {
          await fileStorage.saveData('tracked_expenses', tracked);
        }
      } catch (error) {
        console.warn('Failed to migrate tracked expenses from IndexedDB:', error);
      }
    } else {
      const tracked: TrackedExpense[] = JSON.parse(trackedData);
      await fileStorage.saveData('tracked_expenses', tracked);
    }

    // Migrate license
    let licenseData = localStorage.getItem(LEGACY_STORAGE_KEYS.LICENSE);
    if (!licenseData) {
      try {
        const license = await indexedDBStorage.get<LicenseInfo & { id: string }>('license', 'main');
        if (license) {
          const { id, ...licenseInfo } = license;
          await fileStorage.saveData('license', licenseInfo);
        }
      } catch (error) {
        console.warn('Failed to migrate license from IndexedDB:', error);
      }
    } else {
      const license: LicenseInfo = JSON.parse(licenseData);
      await fileStorage.saveData('license', license);
    }

    // Migrate settings (API key and AI model to server, keep dark mode in browser)
    let settingsData = localStorage.getItem(LEGACY_STORAGE_KEYS.SETTINGS);
    if (!settingsData) {
      try {
        const settings = await indexedDBStorage.get<{ id: string; darkMode: boolean; apiKey: string; aiModel: string }>('settings', 'main');
        if (settings) {
          // Save API key and AI model to server
          await fileStorage.saveData('settings', {
            apiKey: settings.apiKey || '',
            aiModel: settings.aiModel || 'gpt-3.5-turbo-instruct'
          });
          // Keep dark mode in browser session storage
          localStorage.setItem(SESSION_STORAGE_KEYS.DARK_MODE, JSON.stringify(settings.darkMode));
        }
      } catch (error) {
        console.warn('Failed to migrate settings from IndexedDB:', error);
      }
    } else {
      const settings = JSON.parse(settingsData);
      // Save API key and AI model to server
      await fileStorage.saveData('settings', {
        apiKey: settings.apiKey || '',
        aiModel: settings.aiModel || 'gpt-3.5-turbo-instruct'
      });
      // Keep dark mode in browser session storage
      localStorage.setItem(SESSION_STORAGE_KEYS.DARK_MODE, JSON.stringify(settings.darkMode));
    }

    // Mark migration as complete
    localStorage.setItem(migrationKey, 'true');
    console.log('Migration to server storage completed');
  } catch (error) {
    console.error('Migration to server failed:', error);
    // Don't throw - allow app to continue with fallback storage
  }
}

export const storage = {
  // Initialize storage and perform migration
  async init(): Promise<void> {
    try {
      await indexedDBStorage.init();
      await migrateToServerStorage();
    } catch (error) {
      console.error('Storage initialization failed:', error);
      // Continue with localStorage fallback
    }
  },

  // Transactions - Server first, browser fallback
  async getTransactions(): Promise<Transaction[]> {
    try {
      // Try server storage first
      const serverData = await fileStorage.getData('transactions');
      if (serverData && Array.isArray(serverData)) {
        return serverData;
      }
    } catch (error) {
      console.warn('Failed to get transactions from server:', error);
    }

    // Fallback to IndexedDB
    try {
      const transactions = await indexedDBStorage.getAll<Transaction>('transactions');
      if (transactions && transactions.length > 0) {
        return transactions;
      }
    } catch (error) {
      console.warn('Failed to get transactions from IndexedDB:', error);
    }

    // Final fallback to localStorage
    try {
      const data = localStorage.getItem(LEGACY_STORAGE_KEYS.TRANSACTIONS);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Failed to get transactions from localStorage:', error);
      return [];
    }
  },

  async saveTransactions(transactions: Transaction[]): Promise<void> {
    // Save to server first
    try {
      const success = await fileStorage.saveData('transactions', transactions);
      if (success) {
        return; // Success, no need for fallback
      }
    } catch (error) {
      console.warn('Failed to save transactions to server:', error);
    }

    // Fallback to IndexedDB
    try {
      await indexedDBStorage.clear('transactions');
      await indexedDBStorage.putAll('transactions', transactions);
    } catch (error) {
      console.warn('Failed to save transactions to IndexedDB:', error);
      // Final fallback to localStorage
      localStorage.setItem(LEGACY_STORAGE_KEYS.TRANSACTIONS, JSON.stringify(transactions));
    }
  },

  async addTransactions(newTransactions: Transaction[]): Promise<void> {
    const existing = await this.getTransactions();
    
    // Check for duplicates based on date, description, and amount
    const existingSet = new Set(
      existing.map(t => `${t.date}-${t.description}-${t.amount}`)
    );
    
    const uniqueTransactions = newTransactions.filter(t => 
      !existingSet.has(`${t.date}-${t.description}-${t.amount}`)
    );
    
    if (uniqueTransactions.length > 0) {
      const combined = [...existing, ...uniqueTransactions];
      await this.saveTransactions(combined);
    }
    
    return {
      added: uniqueTransactions.length,
      skipped: newTransactions.length - uniqueTransactions.length,
      total: newTransactions.length
    } as any;
  },

  // Budget Categories - Server first, browser fallback
  async getBudgetCategories(): Promise<BudgetCategory[]> {
    try {
      // Try server storage first
      const serverData = await fileStorage.getData('budget');
      if (serverData && Array.isArray(serverData)) {
        return serverData.length > 0 ? serverData : this.getDefaultBudgetCategories();
      }
    } catch (error) {
      console.warn('Failed to get budget categories from server:', error);
    }

    // Fallback to IndexedDB
    try {
      const categories = await indexedDBStorage.getAll<BudgetCategory>('budgetCategories');
      if (categories && categories.length > 0) {
        return categories;
      }
    } catch (error) {
      console.warn('Failed to get budget categories from IndexedDB:', error);
    }

    // Final fallback to localStorage
    try {
      const data = localStorage.getItem(LEGACY_STORAGE_KEYS.BUDGET);
      return data ? JSON.parse(data) : this.getDefaultBudgetCategories();
    } catch (error) {
      console.error('Failed to get budget categories from localStorage:', error);
      return this.getDefaultBudgetCategories();
    }
  },

  async saveBudgetCategories(categories: BudgetCategory[]): Promise<void> {
    // Save to server first
    try {
      const success = await fileStorage.saveData('budget', categories);
      if (success) {
        return; // Success, no need for fallback
      }
    } catch (error) {
      console.warn('Failed to save budget categories to server:', error);
    }

    // Fallback to IndexedDB
    try {
      await indexedDBStorage.clear('budgetCategories');
      await indexedDBStorage.putAll('budgetCategories', categories);
    } catch (error) {
      console.warn('Failed to save budget categories to IndexedDB:', error);
      // Final fallback to localStorage
      localStorage.setItem(LEGACY_STORAGE_KEYS.BUDGET, JSON.stringify(categories));
    }
  },

  getDefaultBudgetCategories(): BudgetCategory[] {
    return [
      // Expense categories
      { id: '1', name: 'Housing', budgetAmount: 1500, currentSpending: 0, type: 'expense' },
      { id: '2', name: 'Transportation', budgetAmount: 400, currentSpending: 0, type: 'expense' },
      { id: '3', name: 'Food', budgetAmount: 600, currentSpending: 0, type: 'expense' },
      { id: '4', name: 'Utilities', budgetAmount: 200, currentSpending: 0, type: 'expense' },
      { id: '5', name: 'Insurance', budgetAmount: 300, currentSpending: 0, type: 'expense' },
      { id: '6', name: 'Healthcare', budgetAmount: 200, currentSpending: 0, type: 'expense' },
      { id: '7', name: 'Entertainment', budgetAmount: 300, currentSpending: 0, type: 'expense' },
      { id: '8', name: 'Shopping', budgetAmount: 250, currentSpending: 0, type: 'expense' },
      { id: '9', name: 'Personal Care', budgetAmount: 100, currentSpending: 0, type: 'expense' },
      { id: '10', name: 'Miscellaneous', budgetAmount: 150, currentSpending: 0, type: 'expense' },
      // Income categories
      { id: '11', name: 'Salary', budgetAmount: 5000, currentSpending: 0, type: 'income' },
      { id: '12', name: 'Freelance', budgetAmount: 1000, currentSpending: 0, type: 'income' },
      { id: '13', name: 'Investments', budgetAmount: 200, currentSpending: 0, type: 'income' },
      { id: '14', name: 'Other Income', budgetAmount: 100, currentSpending: 0, type: 'income' }
    ];
  },

  async addBudgetCategory(name: string, type: 'expense' | 'income' = 'expense'): Promise<BudgetCategory> {
    const categories = await this.getBudgetCategories();
    const newCategory: BudgetCategory = {
      id: Date.now().toString(),
      name,
      budgetAmount: 0,
      currentSpending: 0,
      type
    };
    categories.push(newCategory);
    await this.saveBudgetCategories(categories);
    return newCategory;
  },

  async removeBudgetCategory(categoryId: string): Promise<void> {
    const categories = await this.getBudgetCategories();
    const filtered = categories.filter(cat => cat.id !== categoryId);
    await this.saveBudgetCategories(filtered);
    
    // Update transactions that used this category to "Miscellaneous"
    const transactions = await this.getTransactions();
    const categoryToRemove = categories.find(cat => cat.id === categoryId);
    if (categoryToRemove) {
      const updatedTransactions = transactions.map(transaction => 
        transaction.category === categoryToRemove.name 
          ? { ...transaction, category: 'Miscellaneous' }
          : transaction
      );
      await this.saveTransactions(updatedTransactions);

      // Remove keyword mappings for this category
      const keywords = await this.getKeywordMappings();
      const filteredKeywords = keywords.filter(kw => kw.categoryName !== categoryToRemove.name);
      await this.saveKeywordMappings(filteredKeywords);
    }
  },

  async updateBudgetCategoryName(categoryId: string, newName: string): Promise<void> {
    const categories = await this.getBudgetCategories();
    const categoryIndex = categories.findIndex(cat => cat.id === categoryId);
    
    if (categoryIndex >= 0) {
      const oldName = categories[categoryIndex].name;
      categories[categoryIndex].name = newName;
      await this.saveBudgetCategories(categories);
      
      // Update all transactions that used the old category name
      const transactions = await this.getTransactions();
      const updatedTransactions = transactions.map(transaction => 
        transaction.category === oldName 
          ? { ...transaction, category: newName }
          : transaction
      );
      await this.saveTransactions(updatedTransactions);
      
      // Update history records
      const history = await this.getHistory();
      const updatedHistory = history.map(monthData => ({
        ...monthData,
        transactions: monthData.transactions.map(transaction =>
          transaction.category === oldName
            ? { ...transaction, category: newName }
            : transaction
        ),
        categories: Object.keys(monthData.categories).reduce((acc, key) => {
          const newKey = key === oldName ? newName : key;
          acc[newKey] = monthData.categories[key];
          return acc;
        }, {} as Record<string, number>),
        incomeCategories: Object.keys(monthData.incomeCategories || {}).reduce((acc, key) => {
          const newKey = key === oldName ? newName : key;
          acc[newKey] = (monthData.incomeCategories || {})[key];
          return acc;
        }, {} as Record<string, number>)
      }));
      await this.saveHistory(updatedHistory);

      // Update keyword mappings
      const keywords = await this.getKeywordMappings();
      const updatedKeywords = keywords.map(kw => 
        kw.categoryName === oldName 
          ? { ...kw, categoryName: newName }
          : kw
      );
      await this.saveKeywordMappings(updatedKeywords);
    }
  },

  async getCategoryNames(): Promise<string[]> {
    const categories = await this.getBudgetCategories();
    return categories.map(cat => cat.name);
  },

  // Keyword Mappings - Server first, browser fallback
  async getKeywordMappings(): Promise<KeywordMapping[]> {
    try {
      // Try server storage first
      const serverData = await fileStorage.getData('keywords');
      if (serverData && Array.isArray(serverData)) {
        return serverData;
      }
    } catch (error) {
      console.warn('Failed to get keywords from server:', error);
    }

    // Fallback to IndexedDB
    try {
      const keywords = await indexedDBStorage.getAll<KeywordMapping>('keywordMappings');
      if (keywords && keywords.length > 0) {
        return keywords;
      }
    } catch (error) {
      console.warn('Failed to get keywords from IndexedDB:', error);
    }

    // Final fallback to localStorage
    try {
      const data = localStorage.getItem(LEGACY_STORAGE_KEYS.KEYWORDS);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Failed to get keywords from localStorage:', error);
      return [];
    }
  },

  async saveKeywordMappings(keywords: KeywordMapping[]): Promise<void> {
    // Save to server first
    try {
      const success = await fileStorage.saveData('keywords', keywords);
      if (success) {
        return; // Success, no need for fallback
      }
    } catch (error) {
      console.warn('Failed to save keywords to server:', error);
    }

    // Fallback to IndexedDB
    try {
      await indexedDBStorage.clear('keywordMappings');
      await indexedDBStorage.putAll('keywordMappings', keywords);
    } catch (error) {
      console.warn('Failed to save keywords to IndexedDB:', error);
      // Final fallback to localStorage
      localStorage.setItem(LEGACY_STORAGE_KEYS.KEYWORDS, JSON.stringify(keywords));
    }
  },

  async addKeywordMapping(keyword: string, categoryName: string): Promise<KeywordMapping> {
    const keywords = await this.getKeywordMappings();
    const newMapping: KeywordMapping = {
      id: Date.now().toString(),
      keyword: keyword.toLowerCase().trim(),
      categoryName,
      createdAt: new Date().toISOString()
    };
    keywords.push(newMapping);
    await this.saveKeywordMappings(keywords);
    return newMapping;
  },

  async removeKeywordMapping(keywordId: string): Promise<void> {
    const keywords = await this.getKeywordMappings();
    const filtered = keywords.filter(kw => kw.id !== keywordId);
    await this.saveKeywordMappings(filtered);
  },

  async updateKeywordMapping(keywordId: string, keyword: string, categoryName: string): Promise<void> {
    const keywords = await this.getKeywordMappings();
    const keywordIndex = keywords.findIndex(kw => kw.id === keywordId);
    
    if (keywordIndex >= 0) {
      keywords[keywordIndex] = {
        ...keywords[keywordIndex],
        keyword: keyword.toLowerCase().trim(),
        categoryName
      };
      await this.saveKeywordMappings(keywords);
    }
  },

  // Tracked Expenses - Server first, browser fallback
  async getTrackedExpenses(): Promise<TrackedExpense[]> {
    try {
      // Try server storage first
      const serverData = await fileStorage.getData('tracked_expenses');
      if (serverData && Array.isArray(serverData)) {
        return serverData;
      }
    } catch (error) {
      console.warn('Failed to get tracked expenses from server:', error);
    }

    // Fallback to IndexedDB
    try {
      const tracked = await indexedDBStorage.getAll<TrackedExpense>('trackedExpenses');
      if (tracked && tracked.length > 0) {
        return tracked;
      }
    } catch (error) {
      console.warn('Failed to get tracked expenses from IndexedDB:', error);
    }

    // Final fallback to localStorage
    try {
      const data = localStorage.getItem(LEGACY_STORAGE_KEYS.TRACKED_EXPENSES);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Failed to get tracked expenses from localStorage:', error);
      return [];
    }
  },

  async saveTrackedExpenses(tracked: TrackedExpense[]): Promise<void> {
    // Save to server first
    try {
      const success = await fileStorage.saveData('tracked_expenses', tracked);
      if (success) {
        return; // Success, no need for fallback
      }
    } catch (error) {
      console.warn('Failed to save tracked expenses to server:', error);
    }

    // Fallback to IndexedDB
    try {
      await indexedDBStorage.clear('trackedExpenses');
      await indexedDBStorage.putAll('trackedExpenses', tracked);
    } catch (error) {
      console.warn('Failed to save tracked expenses to IndexedDB:', error);
      // Final fallback to localStorage
      localStorage.setItem(LEGACY_STORAGE_KEYS.TRACKED_EXPENSES, JSON.stringify(tracked));
    }
  },

  async addTrackedExpense(expense: Omit<TrackedExpense, 'id' | 'createdAt' | 'updatedAt'>): Promise<TrackedExpense> {
    const tracked = await this.getTrackedExpenses();
    const newExpense: TrackedExpense = {
      ...expense,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    tracked.push(newExpense);
    await this.saveTrackedExpenses(tracked);
    return newExpense;
  },

  async updateTrackedExpense(expenseId: string, updates: Partial<TrackedExpense>): Promise<void> {
    const tracked = await this.getTrackedExpenses();
    const index = tracked.findIndex(t => t.id === expenseId);
    
    if (index >= 0) {
      tracked[index] = {
        ...tracked[index],
        ...updates,
        updatedAt: new Date().toISOString()
      };
      await this.saveTrackedExpenses(tracked);
    }
  },

  async removeTrackedExpense(expenseId: string): Promise<void> {
    const tracked = await this.getTrackedExpenses();
    const filtered = tracked.filter(t => t.id !== expenseId);
    await this.saveTrackedExpenses(filtered);
  },

  // History - Server first, browser fallback
  async getHistory(): Promise<MonthlyHistory[]> {
    try {
      // Try server storage first
      const serverData = await fileStorage.getData('history');
      if (serverData && Array.isArray(serverData)) {
        return serverData;
      }
    } catch (error) {
      console.warn('Failed to get history from server:', error);
    }

    // Fallback to IndexedDB
    try {
      const history = await indexedDBStorage.getAll<MonthlyHistory>('monthlyHistory');
      if (history && history.length > 0) {
        return history;
      }
    } catch (error) {
      console.warn('Failed to get history from IndexedDB:', error);
    }

    // Final fallback to localStorage
    try {
      const data = localStorage.getItem(LEGACY_STORAGE_KEYS.HISTORY);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Failed to get history from localStorage:', error);
      return [];
    }
  },

  async saveHistory(history: MonthlyHistory[]): Promise<void> {
    // Save to server first
    try {
      const success = await fileStorage.saveData('history', history);
      if (success) {
        return; // Success, no need for fallback
      }
    } catch (error) {
      console.warn('Failed to save history to server:', error);
    }

    // Fallback to IndexedDB
    try {
      await indexedDBStorage.clear('monthlyHistory');
      await indexedDBStorage.putAll('monthlyHistory', history);
    } catch (error) {
      console.warn('Failed to save history to IndexedDB:', error);
      // Final fallback to localStorage
      localStorage.setItem(LEGACY_STORAGE_KEYS.HISTORY, JSON.stringify(history));
    }
  },

  async addMonthlyHistory(monthData: MonthlyHistory): Promise<void> {
    const history = await this.getHistory();
    const existingIndex = history.findIndex(
      h => h.month === monthData.month && h.year === monthData.year
    );
    
    if (existingIndex >= 0) {
      history[existingIndex] = monthData;
    } else {
      history.push(monthData);
    }
    
    await this.saveHistory(history);
  },

  // License Management - Server first, browser fallback
  async getLicenseInfo(): Promise<LicenseInfo> {
    try {
      // Try server storage first
      const serverData = await fileStorage.getData('license');
      if (serverData && typeof serverData === 'object') {
        return serverData as LicenseInfo;
      }
    } catch (error) {
      console.warn('Failed to get license from server:', error);
    }

    // Fallback to IndexedDB
    try {
      const license = await indexedDBStorage.get<LicenseInfo & { id: string }>('license', 'main');
      if (license) {
        const { id, ...licenseInfo } = license;
        return licenseInfo;
      }
    } catch (error) {
      console.warn('Failed to get license from IndexedDB:', error);
    }

    // Final fallback to localStorage
    try {
      const data = localStorage.getItem(LEGACY_STORAGE_KEYS.LICENSE);
      if (data) {
        return JSON.parse(data);
      }
    } catch (error) {
      console.warn('Failed to get license from localStorage:', error);
    }

    // Return default unlicensed state
    return {
      isLicensed: false,
      email: '',
      licenseKey: '',
      features: {
        unlimitedData: false,
        forecastAccess: false,
        extendedTracking: false,
        fullHistory: false
      }
    };
  },

  async saveLicenseInfo(license: LicenseInfo): Promise<void> {
    // Save to server first
    try {
      const success = await fileStorage.saveData('license', license);
      if (success) {
        return; // Success, no need for fallback
      }
    } catch (error) {
      console.warn('Failed to save license to server:', error);
    }

    // Fallback to IndexedDB
    try {
      await indexedDBStorage.put('license', { id: 'main', ...license });
    } catch (error) {
      console.warn('Failed to save license to IndexedDB:', error);
      // Final fallback to localStorage
      localStorage.setItem(LEGACY_STORAGE_KEYS.LICENSE, JSON.stringify(license));
    }
  },

  // Session storage for dark mode (browser only)
  getDarkMode(): boolean {
    try {
      const stored = localStorage.getItem(SESSION_STORAGE_KEYS.DARK_MODE);
      return stored ? JSON.parse(stored) : true; // Default to dark mode
    } catch (error) {
      return true;
    }
  },

  saveDarkMode(darkMode: boolean): void {
    try {
      localStorage.setItem(SESSION_STORAGE_KEYS.DARK_MODE, JSON.stringify(darkMode));
    } catch (error) {
      console.warn('Failed to save dark mode setting:', error);
    }
  },

  // Settings (API key and AI model) - Server first
  async getSettings(): Promise<{ darkMode: boolean; apiKey: string; aiModel: string }> {
    const darkMode = this.getDarkMode();

    try {
      // Try server storage first for API settings
      const serverData = await fileStorage.getData('settings');
      if (serverData && typeof serverData === 'object') {
        return {
          darkMode,
          apiKey: serverData.apiKey || '',
          aiModel: serverData.aiModel || 'gpt-3.5-turbo-instruct'
        };
      }
    } catch (error) {
      console.warn('Failed to get settings from server:', error);
    }

    // Fallback to IndexedDB
    try {
      const settings = await indexedDBStorage.get<{ id: string; darkMode: boolean; apiKey: string; aiModel: string }>('settings', 'main');
      if (settings) {
        return {
          darkMode,
          apiKey: settings.apiKey || '',
          aiModel: settings.aiModel || 'gpt-3.5-turbo-instruct'
        };
      }
    } catch (error) {
      console.warn('Failed to get settings from IndexedDB:', error);
    }

    // Final fallback to localStorage
    try {
      const data = localStorage.getItem(LEGACY_STORAGE_KEYS.SETTINGS);
      if (data) {
        const settings = JSON.parse(data);
        return {
          darkMode,
          apiKey: settings.apiKey || '',
          aiModel: settings.aiModel || 'gpt-3.5-turbo-instruct'
        };
      }
    } catch (error) {
      console.warn('Failed to get settings from localStorage:', error);
    }

    return { darkMode, apiKey: '', aiModel: 'gpt-3.5-turbo-instruct' };
  },

  async saveSettings(settings: { darkMode: boolean; apiKey: string; aiModel: string }): Promise<void> {
    // Save dark mode to browser session storage
    this.saveDarkMode(settings.darkMode);

    // Save API key and AI model to server
    try {
      const success = await fileStorage.saveData('settings', {
        apiKey: settings.apiKey,
        aiModel: settings.aiModel
      });
      if (success) {
        return; // Success, no need for fallback
      }
    } catch (error) {
      console.warn('Failed to save settings to server:', error);
    }

    // Fallback to IndexedDB
    try {
      await indexedDBStorage.put('settings', { id: 'main', ...settings });
    } catch (error) {
      console.warn('Failed to save settings to IndexedDB:', error);
      // Final fallback to localStorage
      localStorage.setItem(LEGACY_STORAGE_KEYS.SETTINGS, JSON.stringify(settings));
    }
  },

  // Clear functions - Clear from server first, then browser storage
  async clearTransactions(): Promise<void> {
    // Clear from server
    try {
      await fileStorage.saveData('transactions', []);
    } catch (error) {
      console.warn('Failed to clear transactions from server:', error);
    }

    // Clear from IndexedDB
    try {
      await indexedDBStorage.clear('transactions');
    } catch (error) {
      console.warn('Failed to clear transactions from IndexedDB:', error);
    }

    // Clear from localStorage
    localStorage.removeItem(LEGACY_STORAGE_KEYS.TRANSACTIONS);
  },

  async clearBudget(): Promise<void> {
    // Clear from server
    try {
      await fileStorage.saveData('budget', []);
    } catch (error) {
      console.warn('Failed to clear budget from server:', error);
    }

    // Clear from IndexedDB
    try {
      await indexedDBStorage.clear('budgetCategories');
    } catch (error) {
      console.warn('Failed to clear budget from IndexedDB:', error);
    }

    // Clear from localStorage
    localStorage.removeItem(LEGACY_STORAGE_KEYS.BUDGET);
  },

  async clearHistory(): Promise<void> {
    // Clear from server
    try {
      await fileStorage.saveData('history', []);
    } catch (error) {
      console.warn('Failed to clear history from server:', error);
    }

    // Clear from IndexedDB
    try {
      await indexedDBStorage.clear('monthlyHistory');
    } catch (error) {
      console.warn('Failed to clear history from IndexedDB:', error);
    }

    // Clear from localStorage
    localStorage.removeItem(LEGACY_STORAGE_KEYS.HISTORY);
  },

  async clearKeywords(): Promise<void> {
    // Clear from server
    try {
      await fileStorage.saveData('keywords', []);
    } catch (error) {
      console.warn('Failed to clear keywords from server:', error);
    }

    // Clear from IndexedDB
    try {
      await indexedDBStorage.clear('keywordMappings');
    } catch (error) {
      console.warn('Failed to clear keywords from IndexedDB:', error);
    }

    // Clear from localStorage
    localStorage.removeItem(LEGACY_STORAGE_KEYS.KEYWORDS);
  },

  async clearTrackedExpenses(): Promise<void> {
    // Clear from server
    try {
      await fileStorage.saveData('tracked_expenses', []);
    } catch (error) {
      console.warn('Failed to clear tracked expenses from server:', error);
    }

    // Clear from IndexedDB
    try {
      await indexedDBStorage.clear('trackedExpenses');
    } catch (error) {
      console.warn('Failed to clear tracked expenses from IndexedDB:', error);
    }

    // Clear from localStorage
    localStorage.removeItem(LEGACY_STORAGE_KEYS.TRACKED_EXPENSES);
  },

  async clearLicense(): Promise<void> {
    // Clear from server
    try {
      await fileStorage.saveData('license', {
        isLicensed: false,
        email: '',
        licenseKey: '',
        features: {
          unlimitedData: false,
          forecastAccess: false,
          extendedTracking: false,
          fullHistory: false
        }
      });
    } catch (error) {
      console.warn('Failed to clear license from server:', error);
    }

    // Clear from IndexedDB
    try {
      await indexedDBStorage.delete('license', 'main');
    } catch (error) {
      console.warn('Failed to clear license from IndexedDB:', error);
    }

    // Clear from localStorage
    localStorage.removeItem(LEGACY_STORAGE_KEYS.LICENSE);
  },

  async clearAll(): Promise<void> {
    // Clear from server
    try {
      await fileStorage.clearAll();
    } catch (error) {
      console.warn('Failed to clear all data from server:', error);
    }

    // Clear from IndexedDB
    try {
      await indexedDBStorage.clearAll();
    } catch (error) {
      console.warn('Failed to clear all data from IndexedDB:', error);
    }

    // Clear from localStorage (legacy data only, keep session data)
    Object.values(LEGACY_STORAGE_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });
  },

  async clearMonthData(month: string, year: number): Promise<void> {
    const history = await this.getHistory();
    const filtered = history.filter(h => !(h.month === month && h.year === year));
    await this.saveHistory(filtered);
  }
};