import { Transaction, ForecastData } from '../types';

export function generateForecast(transactions: Transaction[]): ForecastData[] {
  if (transactions.length === 0) {
    return [];
  }

  // Group transactions by month
  const monthlySpending = transactions.reduce((acc, transaction) => {
    const date = new Date(transaction.date);
    const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    
    if (!acc[monthKey]) {
      acc[monthKey] = 0;
    }
    
    // Only count expenses (negative amounts)
    if (transaction.amount < 0) {
      acc[monthKey] += Math.abs(transaction.amount);
    }
    
    return acc;
  }, {} as Record<string, number>);

  const monthlyData = Object.entries(monthlySpending)
    .map(([monthKey, spending]) => {
      const [year, month] = monthKey.split('-');
      return {
        year: parseInt(year),
        month: parseInt(month),
        spending
      };
    })
    .sort((a, b) => a.year - b.year || a.month - b.month);

  if (monthlyData.length < 2) {
    // Not enough data for forecasting
    return [];
  }

  // Calculate trend (simple linear regression)
  const n = monthlyData.length;
  const sumX = monthlyData.reduce((sum, _, index) => sum + index, 0);
  const sumY = monthlyData.reduce((sum, data) => sum + data.spending, 0);
  const sumXY = monthlyData.reduce((sum, data, index) => sum + index * data.spending, 0);
  const sumXX = monthlyData.reduce((sum, _, index) => sum + index * index, 0);

  const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  const intercept = (sumY - slope * sumX) / n;

  // Generate forecasts for next 6 months
  const forecast: ForecastData[] = [];
  const lastMonth = monthlyData[monthlyData.length - 1];
  
  for (let i = 1; i <= 6; i++) {
    const futureMonth = new Date(lastMonth.year, lastMonth.month - 1 + i, 1);
    const projectedSpending = Math.max(0, intercept + slope * (n + i - 1));
    
    // Calculate confidence based on variance
    const variance = monthlyData.reduce((sum, data, index) => {
      const predicted = intercept + slope * index;
      return sum + Math.pow(data.spending - predicted, 2);
    }, 0) / n;
    
    const confidence = Math.max(0.1, Math.min(0.9, 1 / (1 + variance / 10000)));

    forecast.push({
      month: futureMonth.toLocaleString('default', { month: 'long' }),
      year: futureMonth.getFullYear(),
      projectedSpending: Math.round(projectedSpending),
      confidence: Math.round(confidence * 100) / 100
    });
  }

  return forecast;
}

export function calculateTrend(currentValue: number, previousValue: number): {
  percentage: number;
  direction: 'up' | 'down' | 'stable';
} {
  if (previousValue === 0) {
    return { percentage: 0, direction: 'stable' };
  }

  const percentage = ((currentValue - previousValue) / previousValue) * 100;
  const direction = percentage > 5 ? 'up' : percentage < -5 ? 'down' : 'stable';

  return {
    percentage: Math.abs(percentage),
    direction
  };
}