import React, { useState, useEffect } from 'react';
import { MonthlyHistory, KeywordMapping, BudgetCategory } from '../types';
import { storage } from '../utils/storage';
import { licenseManager } from '../utils/licenseManager';
import KeywordManager from '../components/KeywordManager';
import LicenseManager from '../components/LicenseManager';
import { 
  Trash2, 
  AlertTriangle, 
  CheckCircle, 
  Database,
  Calendar,
  FileText,
  Tag,
  Loader2,
  Shield,
  Key
} from 'lucide-react';

export default function AdminTab() {
  const [history, setHistory] = useState<MonthlyHistory[]>([]);
  const [keywords, setKeywords] = useState<KeywordMapping[]>([]);
  const [budgetCategories, setBudgetCategories] = useState<BudgetCategory[]>([]);
  const [selectedMonths, setSelectedMonths] = useState<string[]>([]);
  const [showConfirmDialog, setShowConfirmDialog] = useState<string | null>(null);
  const [activeSection, setActiveSection] = useState<'overview' | 'keywords' | 'license'>('overview');
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState({
    totalTransactions: 0,
    totalCategories: 0,
    totalHistory: 0,
    totalKeywords: 0,
    storageSize: 0
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const [historyData, keywordData, transactions, categories] = await Promise.all([
        storage.getHistory(),
        storage.getKeywordMappings(),
        storage.getTransactions(),
        storage.getBudgetCategories()
      ]);
      
      setHistory(historyData);
      setKeywords(keywordData);
      setBudgetCategories(categories);
      
      // Calculate storage stats (legacy localStorage data only)
      const storageSize = Object.keys(localStorage)
        .filter(key => key.startsWith('budget_') && !key.includes('session'))
        .reduce((total, key) => {
          return total + (localStorage.getItem(key)?.length || 0);
        }, 0);

      setStats({
        totalTransactions: transactions.length,
        totalCategories: categories.length,
        totalHistory: historyData.length,
        totalKeywords: keywordData.length,
        storageSize: Math.round(storageSize / 1024) // Convert to KB
      });
    } catch (err) {
      console.error('Failed to load admin data:', err);
      setError('Failed to load admin data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleMonthSelection = (monthKey: string) => {
    setSelectedMonths(prev => 
      prev.includes(monthKey) 
        ? prev.filter(key => key !== monthKey)
        : [...prev, monthKey]
    );
  };

  const clearSelectedMonths = async () => {
    try {
      setIsDeleting(true);
      setError(null);

      for (const monthKey of selectedMonths) {
        const [month, year] = monthKey.split('-');
        await storage.clearMonthData(month, parseInt(year));
      }
      
      setSelectedMonths([]);
      await loadData();
      setShowConfirmDialog(null);
    } catch (err) {
      console.error('Failed to clear selected months:', err);
      setError('Failed to clear selected months');
    } finally {
      setIsDeleting(false);
    }
  };

  const clearAllData = async () => {
    try {
      setIsDeleting(true);
      setError(null);

      await storage.clearAll();
      await loadData();
      setSelectedMonths([]);
      setShowConfirmDialog(null);
    } catch (err) {
      console.error('Failed to clear all data:', err);
      setError('Failed to clear all data');
    } finally {
      setIsDeleting(false);
    }
  };

  const exportData = async () => {
    try {
      const [transactions, budget, history, keywords, settings, license] = await Promise.all([
        storage.getTransactions(),
        storage.getBudgetCategories(),
        storage.getHistory(),
        storage.getKeywordMappings(),
        storage.getSettings(),
        storage.getLicenseInfo()
      ]);

      const data = {
        transactions,
        budget,
        history,
        keywords,
        settings,
        license,
        exportDate: new Date().toISOString(),
        version: '2.0.0'
      };

      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `budget-data-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Export failed:', err);
      setError('Failed to export data');
    }
  };

  const handleAddKeyword = async (keyword: string, categoryName: string) => {
    try {
      await storage.addKeywordMapping(keyword, categoryName);
      await loadData();
    } catch (err) {
      console.error('Failed to add keyword:', err);
      setError('Failed to add keyword mapping');
    }
  };

  const handleRemoveKeyword = async (keywordId: string) => {
    try {
      await storage.removeKeywordMapping(keywordId);
      await loadData();
    } catch (err) {
      console.error('Failed to remove keyword:', err);
      setError('Failed to remove keyword mapping');
    }
  };

  const handleUpdateKeyword = async (keywordId: string, keyword: string, categoryName: string) => {
    try {
      await storage.updateKeywordMapping(keywordId, keyword, categoryName);
      await loadData();
    } catch (err) {
      console.error('Failed to update keyword:', err);
      setError('Failed to update keyword mapping');
    }
  };

  const handleLicenseUpdate = async () => {
    // Refresh license manager and reload data
    await licenseManager.initialize();
    await loadData();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-400 mx-auto mb-4" />
          <p className="text-white">Loading admin data...</p>
        </div>
      </div>
    );
  }

  const sortedHistory = [...history].sort((a, b) => {
    if (a.year !== b.year) return b.year - a.year;
    return new Date(`${b.month} 1, ${b.year}`).getMonth() - new Date(`${a.month} 1, ${a.year}`).getMonth();
  });

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-white">Admin Panel</h1>
        <p className="text-gray-400 mt-1">
          Manage your budget data, license, keyword mappings, and clear historical records
        </p>
      </div>

      {error && (
        <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400">
          {error}
        </div>
      )}

      {/* Section Navigation */}
      <div className="flex space-x-1 bg-gray-800 rounded-lg p-1">
        <button
          onClick={() => setActiveSection('license')}
          className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-colors ${
            activeSection === 'license'
              ? 'bg-blue-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-gray-700'
          }`}
        >
          <Shield size={16} />
          <span>License</span>
        </button>
        <button
          onClick={() => setActiveSection('overview')}
          className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-colors ${
            activeSection === 'overview'
              ? 'bg-blue-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-gray-700'
          }`}
        >
          <Database size={16} />
          <span>Data Management</span>
        </button>
        <button
          onClick={() => setActiveSection('keywords')}
          className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-colors ${
            activeSection === 'keywords'
              ? 'bg-blue-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-gray-700'
          }`}
        >
          <Tag size={16} />
          <span>Keywords</span>
        </button>
      </div>

      {activeSection === 'license' ? (
        <LicenseManager 
          licenseInfo={licenseManager.getLicenseInfo()} 
          onLicenseUpdate={handleLicenseUpdate}
        />
      ) : activeSection === 'overview' ? (
        <>
          {/* Storage Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
            <div className="bg-gray-800 rounded-lg p-6">
              <div className="flex items-center space-x-3">
                <div className="p-3 bg-blue-500/20 rounded-lg">
                  <FileText className="text-blue-400" size={24} />
                </div>
                <div>
                  <p className="text-gray-400 text-sm">Transactions</p>
                  <p className="text-2xl font-bold text-white">{stats.totalTransactions}</p>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-6">
              <div className="flex items-center space-x-3">
                <div className="p-3 bg-green-500/20 rounded-lg">
                  <Calendar className="text-green-400" size={24} />
                </div>
                <div>
                  <p className="text-gray-400 text-sm">History Months</p>
                  <p className="text-2xl font-bold text-white">{stats.totalHistory}</p>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-6">
              <div className="flex items-center space-x-3">
                <div className="p-3 bg-purple-500/20 rounded-lg">
                  <Database className="text-purple-400" size={24} />
                </div>
                <div>
                  <p className="text-gray-400 text-sm">Categories</p>
                  <p className="text-2xl font-bold text-white">{stats.totalCategories}</p>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-6">
              <div className="flex items-center space-x-3">
                <div className="p-3 bg-yellow-500/20 rounded-lg">
                  <Tag className="text-yellow-400" size={24} />
                </div>
                <div>
                  <p className="text-gray-400 text-sm">Keywords</p>
                  <p className="text-2xl font-bold text-white">{stats.totalKeywords}</p>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-6">
              <div className="flex items-center space-x-3">
                <div className="p-3 bg-orange-500/20 rounded-lg">
                  <Database className="text-orange-400" size={24} />
                </div>
                <div>
                  <p className="text-gray-400 text-sm">Storage Used</p>
                  <p className="text-2xl font-bold text-white">{stats.storageSize} KB</p>
                </div>
              </div>
            </div>
          </div>

          {/* Data Management */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Month Selection */}
            <div className="bg-gray-800 rounded-lg p-6">
              <h2 className="text-lg font-medium text-white mb-4">Clear Monthly Data</h2>
              
              {history.length === 0 ? (
                <p className="text-gray-400 text-center py-4">No monthly history available</p>
              ) : (
                <>
                  <div className="space-y-2 mb-4 max-h-64 overflow-y-auto">
                    {sortedHistory.map((month) => {
                      const monthKey = `${month.month}-${month.year}`;
                      return (
                        <label key={monthKey} className="flex items-center space-x-3 p-3 bg-gray-700 rounded-lg cursor-pointer hover:bg-gray-600 transition-colors">
                          <input
                            type="checkbox"
                            checked={selectedMonths.includes(monthKey)}
                            onChange={() => handleMonthSelection(monthKey)}
                            className="rounded text-blue-500 focus:ring-blue-500 focus:ring-2"
                          />
                          <div className="flex-1">
                            <p className="text-white">{month.month} {month.year}</p>
                            <p className="text-sm text-gray-400">
                              {month.transactions.length} transactions, ${month.totalSpent.toFixed(2)} spent
                            </p>
                          </div>
                        </label>
                      );
                    })}
                  </div>

                  <button
                    onClick={() => setShowConfirmDialog('months')}
                    disabled={selectedMonths.length === 0 || isDeleting}
                    className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
                  >
                    {isDeleting ? <Loader2 size={16} className="animate-spin" /> : <Trash2 size={16} />}
                    <span>
                      {isDeleting ? 'Deleting...' : `Delete Selected Months (${selectedMonths.length})`}
                    </span>
                  </button>
                </>
              )}
            </div>

            {/* Global Actions */}
            <div className="bg-gray-800 rounded-lg p-6">
              <h2 className="text-lg font-medium text-white mb-4">Global Actions</h2>
              
              <div className="space-y-4">
                <button
                  onClick={exportData}
                  className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  <Database size={16} />
                  <span>Export All Data</span>
                </button>

                <div className="border-t border-gray-700 pt-4">
                  <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-4">
                    <div className="flex items-center space-x-2 text-red-400 mb-2">
                      <AlertTriangle size={20} />
                      <span className="font-medium">Danger Zone</span>
                    </div>
                    <p className="text-sm text-red-300">
                      This will permanently delete all your budget data including transactions, 
                      categories, history, keyword mappings, license info, and settings. This action cannot be undone.
                    </p>
                  </div>

                  <button
                    onClick={() => setShowConfirmDialog('all')}
                    disabled={isDeleting}
                    className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
                  >
                    {isDeleting ? <Loader2 size={16} className="animate-spin" /> : <Trash2 size={16} />}
                    <span>{isDeleting ? 'Clearing...' : 'Clear All Data'}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </>
      ) : (
        <KeywordManager
          keywords={keywords}
          categories={budgetCategories}
          onAddKeyword={handleAddKeyword}
          onRemoveKeyword={handleRemoveKeyword}
          onUpdateKeyword={handleUpdateKeyword}
        />
      )}

      {/* Confirmation Dialog */}
      {showConfirmDialog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center space-x-3 mb-4">
              <AlertTriangle className="text-red-400" size={24} />
              <h3 className="text-lg font-medium text-white">Confirm Deletion</h3>
            </div>
            
            <p className="text-gray-300 mb-6">
              {showConfirmDialog === 'all' 
                ? 'Are you sure you want to delete ALL budget data including license information? This action cannot be undone.'
                : `Are you sure you want to delete data for ${selectedMonths.length} selected month(s)? This action cannot be undone.`
              }
            </p>
            
            <div className="flex space-x-3">
              <button
                onClick={() => setShowConfirmDialog(null)}
                disabled={isDeleting}
                className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 disabled:opacity-50 text-white rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={showConfirmDialog === 'all' ? clearAllData : clearSelectedMonths}
                disabled={isDeleting}
                className="flex-1 flex items-center justify-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 disabled:opacity-50 text-white rounded-lg transition-colors"
              >
                {isDeleting ? <Loader2 size={16} className="animate-spin" /> : null}
                <span>{isDeleting ? 'Deleting...' : 'Delete'}</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Success Message */}
      {showConfirmDialog === null && selectedMonths.length === 0 && (
        <div className="fixed bottom-4 right-4 bg-green-600 text-white px-4 py-2 rounded-lg shadow-lg animate-fade-in">
          <div className="flex items-center space-x-2">
            <CheckCircle size={16} />
            <span>Data cleared successfully</span>
          </div>
        </div>
      )}
    </div>
  );
}