import React, { useState, useRef } from 'react';
import { storage } from '../utils/storage';
import { Transaction, BudgetCategory, MonthlyHistory } from '../types';
import { 
  Download, 
  Upload, 
  FileText, 
  AlertCircle, 
  CheckCircle,
  Database,
  Loader2
} from 'lucide-react';

export default function DataTab() {
  const [importStatus, setImportStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [importMessage, setImportMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [stats, setStats] = useState({
    transactions: 0,
    history: 0,
    categories: 0,
    totalSpent: 0,
    dateRange: null as { earliest: Date; latest: Date } | null
  });
  const fileInputRef = useRef<HTMLInputElement>(null);

  React.useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      setIsLoading(true);
      const [transactions, history, categories] = await Promise.all([
        storage.getTransactions(),
        storage.getHistory(),
        storage.getBudgetCategories()
      ]);

      const totalSpent = transactions
        .filter(t => t.amount < 0)
        .reduce((sum, t) => sum + Math.abs(t.amount), 0);

      const dateRange = transactions.length > 0 ? {
        earliest: new Date(Math.min(...transactions.map(t => new Date(t.date).getTime()))),
        latest: new Date(Math.max(...transactions.map(t => new Date(t.date).getTime())))
      } : null;

      setStats({
        transactions: transactions.length,
        history: history.length,
        categories: categories.length,
        totalSpent,
        dateRange
      });
    } catch (err) {
      console.error('Failed to load stats:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const exportToJSON = async () => {
    try {
      setIsExporting(true);
      
      const [transactions, budget, history, keywords, settings] = await Promise.all([
        storage.getTransactions(),
        storage.getBudgetCategories(),
        storage.getHistory(),
        storage.getKeywordMappings(),
        storage.getSettings()
      ]);

      const data = {
        transactions,
        budget,
        history,
        keywords,
        settings,
        exportDate: new Date().toISOString(),
        version: '1.1.3'
      };

      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `budget-data-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Export failed:', err);
      setImportStatus('error');
      setImportMessage('Failed to export data');
    } finally {
      setIsExporting(false);
    }
  };

  const exportToCSV = async () => {
    try {
      setIsExporting(true);
      const transactions = await storage.getTransactions();
      
      if (transactions.length === 0) {
        setImportStatus('error');
        setImportMessage('No transactions to export');
        return;
      }

      const csvHeaders = ['Date', 'Description', 'Category', 'Amount'];
      const csvRows = transactions.map(t => [
        t.date,
        `"${t.description.replace(/"/g, '""')}"`,
        t.category,
        t.amount.toString()
      ]);

      const csvContent = [csvHeaders, ...csvRows]
        .map(row => row.join(','))
        .join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `transactions-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (err) {
      console.error('CSV export failed:', err);
      setImportStatus('error');
      setImportMessage('Failed to export CSV');
    } finally {
      setIsExporting(false);
    }
  };

  const handleFileImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Check file size (limit to 10MB)
    if (file.size > 10 * 1024 * 1024) {
      setImportStatus('error');
      setImportMessage('File is too large. Please use files smaller than 10MB.');
      return;
    }

    setIsLoading(true);
    setImportStatus('idle');
    setImportMessage('');

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const content = e.target?.result as string;
        
        if (file.name.endsWith('.json')) {
          await importFromJSON(content);
        } else if (file.name.endsWith('.csv')) {
          await importFromCSV(content);
        } else {
          throw new Error('Unsupported file format. Please use JSON or CSV files.');
        }
      } catch (error) {
        setImportStatus('error');
        setImportMessage(error instanceof Error ? error.message : 'Failed to import file');
      } finally {
        setIsLoading(false);
      }
    };

    reader.onerror = () => {
      setImportStatus('error');
      setImportMessage('Failed to read file');
      setIsLoading(false);
    };

    reader.readAsText(file);
    
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const importFromJSON = async (content: string) => {
    let data;
    try {
      data = JSON.parse(content);
    } catch (parseError) {
      // Try to sanitize and recover from malformed JSON
      const sanitized = content
        .replace(/[\x00-\x1F\x7F-\x9F]/g, '') // Remove control characters
        .replace(/\\(?!["\\/bfnrt])/g, '\\\\'); // Fix bad escapes
      
      try {
        data = JSON.parse(sanitized);
      } catch {
        throw new Error('Invalid JSON format. Please check the file structure.');
      }
    }
    
    // Validate data structure
    if (!data.transactions || !Array.isArray(data.transactions)) {
      throw new Error('Invalid JSON format: missing or invalid transactions array');
    }

    // Check transaction count limit
    if (data.transactions.length > 10000) {
      throw new Error('Too many transactions. Please limit to 10,000 transactions per import.');
    }

    let importStats = {
      added: 0,
      skipped: 0,
      categoryPreserved: 0
    };

    // Import transactions with duplicate detection
    if (data.transactions.length > 0) {
      const validTransactions: Transaction[] = data.transactions.map((t: any, index: number) => {
        if (!t.date || !t.description || typeof t.amount !== 'number') {
          throw new Error(`Invalid transaction at line ${index + 2}: missing required fields`);
        }
        return {
          id: t.id || `imported-${Date.now()}-${index}`,
          date: t.date,
          description: t.description,
          category: t.category || 'Miscellaneous',
          amount: t.amount,
          originalData: t.originalData
        };
      });

      const result = await storage.addTransactions(validTransactions) as any;
      if (result && typeof result === 'object') {
        importStats.added = result.added;
        importStats.skipped = result.skipped;
      } else {
        importStats.added = validTransactions.length;
      }
    }

    // Import budget categories if available
    if (data.budget && Array.isArray(data.budget)) {
      const validCategories: BudgetCategory[] = data.budget.map((c: any) => ({
        id: c.id || Date.now().toString(),
        name: c.name || 'Unknown',
        budgetAmount: typeof c.budgetAmount === 'number' ? c.budgetAmount : 0,
        currentSpending: typeof c.currentSpending === 'number' ? c.currentSpending : 0
      }));
      await storage.saveBudgetCategories(validCategories);
    }

    // Import history if available
    if (data.history && Array.isArray(data.history)) {
      const validHistory: MonthlyHistory[] = data.history.map((h: any) => ({
        month: h.month || 'Unknown',
        year: typeof h.year === 'number' ? h.year : new Date().getFullYear(),
        totalSpent: typeof h.totalSpent === 'number' ? h.totalSpent : 0,
        transactions: Array.isArray(h.transactions) ? h.transactions : [],
        categories: typeof h.categories === 'object' ? h.categories : {}
      }));
      
      for (const historyEntry of validHistory) {
        await storage.addMonthlyHistory(historyEntry);
      }
    }

    // Import keyword mappings if available
    if (data.keywords && Array.isArray(data.keywords)) {
      await storage.saveKeywordMappings(data.keywords);
    }

    // Import settings if available
    if (data.settings && typeof data.settings === 'object') {
      const currentSettings = await storage.getSettings();
      await storage.saveSettings({
        darkMode: typeof data.settings.darkMode === 'boolean' ? data.settings.darkMode : currentSettings.darkMode,
        apiKey: typeof data.settings.apiKey === 'string' ? data.settings.apiKey : currentSettings.apiKey
      });
    }

    await loadStats();
    setImportStatus('success');
    setImportMessage(`Successfully imported - Added: ${importStats.added}, Skipped duplicates: ${importStats.skipped}`);
  };

  const importFromCSV = async (content: string) => {
    const lines = content.trim().split('\n');
    if (lines.length < 2) {
      throw new Error('CSV file must contain at least a header row and one data row');
    }

    if (lines.length > 10001) { // Including header
      throw new Error('Too many rows. Please limit to 10,000 transactions per CSV.');
    }

    const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
    const transactions: Transaction[] = [];

    // Find column indices
    const dateIndex = headers.findIndex(h => h.includes('date'));
    const descriptionIndex = headers.findIndex(h => h.includes('description') || h.includes('memo') || h.includes('transaction'));
    const amountIndex = headers.findIndex(h => h.includes('amount') || h.includes('value'));
    const categoryIndex = headers.findIndex(h => h.includes('category') || h.includes('type'));

    if (dateIndex === -1 || descriptionIndex === -1 || amountIndex === -1) {
      throw new Error('CSV must contain date, description, and amount columns');
    }

    for (let i = 1; i < lines.length; i++) {
      try {
        const columns = lines[i].split(',').map(c => c.trim().replace(/^"|"$/g, ''));
        
        if (columns.length < Math.max(dateIndex, descriptionIndex, amountIndex) + 1) {
          continue; // Skip invalid rows
        }

        const amount = parseFloat(columns[amountIndex]);
        if (isNaN(amount)) {
          continue; // Skip rows with invalid amounts
        }

        transactions.push({
          id: `csv-import-${Date.now()}-${i}`,
          date: columns[dateIndex],
          description: columns[descriptionIndex],
          category: categoryIndex >= 0 && columns[categoryIndex] ? columns[categoryIndex] : 'Miscellaneous',
          amount: amount,
          originalData: lines[i]
        });
      } catch (rowError) {
        console.warn(`Skipping invalid row ${i + 1}:`, rowError);
        continue;
      }
    }

    if (transactions.length === 0) {
      throw new Error('No valid transactions found in CSV file');
    }

    const result = await storage.addTransactions(transactions) as any;
    await loadStats();
    
    let successMessage = `Successfully imported ${transactions.length} transactions from CSV`;
    if (result && typeof result === 'object') {
      successMessage = `CSV import complete - Added: ${result.added} transactions`;
      if (result.skipped > 0) {
        successMessage += `, Skipped ${result.skipped} duplicates`;
      }
      if (result.internalTransfers > 0) {
        successMessage += `, Detected ${result.internalTransfers} internal transfers`;
      }
      if (result.monthsProcessed > 0) {
        successMessage += `, Organized into ${result.monthsProcessed} months`;
      }
    }
    
    setImportStatus('success');
    setImportMessage(successMessage);
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-white">Data Management</h1>
        <p className="text-gray-400 mt-1">
          Export and import your budget data in various formats
        </p>
      </div>

      {/* Status Messages */}
      {importStatus === 'success' && (
        <div className="flex items-center space-x-2 p-4 bg-green-500/10 border border-green-500/20 rounded-lg text-green-400">
          <CheckCircle size={20} />
          <span>{importMessage}</span>
        </div>
      )}

      {importStatus === 'error' && (
        <div className="flex items-center space-x-2 p-4 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400">
          <AlertCircle size={20} />
          <span>{importMessage}</span>
        </div>
      )}

      {/* Data Statistics */}
      {isLoading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-blue-400" />
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-gray-800 rounded-lg p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-blue-500/20 rounded-lg">
                <FileText className="text-blue-400" size={24} />
              </div>
              <div>
                <p className="text-gray-400 text-sm">Transactions</p>
                <p className="text-2xl font-bold text-white">{stats.transactions}</p>
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-green-500/20 rounded-lg">
                <Database className="text-green-400" size={24} />
              </div>
              <div>
                <p className="text-gray-400 text-sm">History Months</p>
                <p className="text-2xl font-bold text-white">{stats.history}</p>
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-purple-500/20 rounded-lg">
                <Database className="text-purple-400" size={24} />
              </div>
              <div>
                <p className="text-gray-400 text-sm">Categories</p>
                <p className="text-2xl font-bold text-white">{stats.categories}</p>
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-red-500/20 rounded-lg">
                <Database className="text-red-400" size={24} />
              </div>
              <div>
                <p className="text-gray-400 text-sm">Total Spent</p>
                <p className="text-2xl font-bold text-white">${stats.totalSpent.toFixed(0)}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {stats.dateRange && (
        <div className="bg-gray-800 rounded-lg p-4">
          <p className="text-gray-400 text-sm">
            Data range: {stats.dateRange.earliest.toLocaleDateString()} - {stats.dateRange.latest.toLocaleDateString()}
          </p>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Export Section */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-lg font-medium text-white mb-4">Export Data</h2>
          <p className="text-gray-400 text-sm mb-6">
            Download your budget data for backup or use in other applications
          </p>
          
          <div className="space-y-3">
            <button
              onClick={exportToJSON}
              disabled={isExporting}
              className="w-full flex items-center space-x-3 p-4 bg-gray-700 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg transition-colors"
            >
              {isExporting ? <Loader2 className="text-blue-400 animate-spin" size={20} /> : <Download className="text-blue-400" size={20} />}
              <div className="text-left">
                <p className="text-white font-medium">Export as JSON</p>
                <p className="text-sm text-gray-400">Complete backup with all data and settings</p>
              </div>
            </button>

            <button
              onClick={exportToCSV}
              disabled={stats.transactions === 0 || isExporting}
              className="w-full flex items-center space-x-3 p-4 bg-gray-700 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg transition-colors"
            >
              {isExporting ? <Loader2 className="text-green-400 animate-spin" size={20} /> : <FileText className="text-green-400" size={20} />}
              <div className="text-left">
                <p className="text-white font-medium">Export as CSV</p>
                <p className="text-sm text-gray-400">Transactions only, for spreadsheet use</p>
              </div>
            </button>
          </div>
        </div>

        {/* Import Section */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-lg font-medium text-white mb-4">Import Data</h2>
          <p className="text-gray-400 text-sm mb-6">
            Upload JSON backup or CSV transaction files to restore or add data
          </p>
          
          <div className="space-y-4">
            <input
              ref={fileInputRef}
              type="file"
              accept=".json,.csv"
              onChange={handleFileImport}
              disabled={isLoading}
              className="hidden"
            />
            
            <button
              onClick={() => fileInputRef.current?.click()}
              disabled={isLoading}
              className="w-full flex items-center space-x-3 p-4 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg transition-colors"
            >
              {isLoading ? <Loader2 className="text-white animate-spin" size={20} /> : <Upload className="text-white" size={20} />}
              <div className="text-left">
                <p className="text-white font-medium">
                  {isLoading ? 'Processing...' : 'Choose File to Import'}
                </p>
                <p className="text-sm text-blue-200">Supports JSON and CSV formats</p>
              </div>
            </button>

            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
              <h4 className="text-blue-300 font-medium mb-2">Import Guidelines</h4>
              <ul className="text-sm text-blue-200 space-y-1">
                <li>• JSON: Complete data restore from previous export</li>
                <li>• CSV: Requires Date, Description, and Amount columns</li>
                <li>• Duplicate transactions are automatically detected and skipped</li>
                <li>• File size limit: 10MB, Transaction limit: 10,000 per file</li>
                <li>• Large files may take a moment to process</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}