import React, { useState, useEffect } from 'react';
import { MonthlyHistory, Transaction } from '../types';
import { storage } from '../utils/storage';
import { Calendar, TrendingUp, TrendingDown, Eye, Archive, Loader2, DollarSign, PiggyBank } from 'lucide-react';

export default function HistoryTab() {
  const [history, setHistory] = useState<MonthlyHistory[]>([]);
  const [selectedMonth, setSelectedMonth] = useState<MonthlyHistory | null>(null);
  const [showTransactions, setShowTransactions] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadHistory();
    checkForCompleteMonths();
  }, []);

  const loadHistory = async () => {
    try {
      const historyData = await storage.getHistory();
      setHistory(historyData);
    } catch (err) {
      console.error('Failed to load history:', err);
      setError('Failed to load history data');
    }
  };

  const checkForCompleteMonths = async () => {
    try {
      const transactions = await storage.getTransactions();
      const currentDate = new Date();
      const currentMonth = currentDate.getMonth();
      const currentYear = currentDate.getFullYear();

      // Group transactions by month
      const monthlyData = transactions.reduce((acc, transaction) => {
        const date = new Date(transaction.date);
        const month = date.getMonth();
        const year = date.getFullYear();
        
        // Skip current month (incomplete)
        if (month === currentMonth && year === currentYear) {
          return acc;
        }

        const monthKey = `${year}-${month}`;
        if (!acc[monthKey]) {
          acc[monthKey] = {
            month: date.toLocaleString('default', { month: 'long' }),
            year,
            transactions: [],
            categories: {},
            incomeCategories: {}
          };
        }

        acc[monthKey].transactions.push(transaction);
        
        // Calculate category totals
        if (transaction.amount < 0) {
          // Expense
          if (!acc[monthKey].categories[transaction.category]) {
            acc[monthKey].categories[transaction.category] = 0;
          }
          acc[monthKey].categories[transaction.category] += Math.abs(transaction.amount);
        } else {
          // Income
          if (!acc[monthKey].incomeCategories[transaction.category]) {
            acc[monthKey].incomeCategories[transaction.category] = 0;
          }
          acc[monthKey].incomeCategories[transaction.category] += transaction.amount;
        }

        return acc;
      }, {} as Record<string, any>);

      // Create monthly history entries
      const existingHistory = await storage.getHistory();
      let hasNewData = false;

      for (const monthData of Object.values(monthlyData) as any[]) {
        const totalSpent = monthData.transactions
          .filter((t: Transaction) => t.amount < 0)
          .reduce((sum: number, t: Transaction) => sum + Math.abs(t.amount), 0);
        
        const totalIncome = monthData.transactions
          .filter((t: Transaction) => t.amount > 0)
          .reduce((sum: number, t: Transaction) => sum + t.amount, 0);

        const historyEntry: MonthlyHistory = {
          month: monthData.month,
          year: monthData.year,
          totalSpent,
          totalIncome,
          netIncome: totalIncome - totalSpent,
          transactions: monthData.transactions,
          categories: monthData.categories,
          incomeCategories: monthData.incomeCategories
        };

        const exists = existingHistory.find(
          h => h.month === historyEntry.month && h.year === historyEntry.year
        );

        if (!exists) {
          await storage.addMonthlyHistory(historyEntry);
          hasNewData = true;
        }
      }

      if (hasNewData) {
        await loadHistory();
      }
    } catch (err) {
      console.error('Failed to check for complete months:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const getTrendComparedToPrevious = (currentMonth: MonthlyHistory, field: 'totalSpent' | 'totalIncome' | 'netIncome') => {
    const sortedHistory = [...history].sort((a, b) => {
      if (a.year !== b.year) return a.year - b.year;
      return new Date(`${a.month} 1, ${a.year}`).getMonth() - new Date(`${b.month} 1, ${b.year}`).getMonth();
    });

    const currentIndex = sortedHistory.findIndex(
      h => h.month === currentMonth.month && h.year === currentMonth.year
    );

    if (currentIndex <= 0) return null;

    const previousMonth = sortedHistory[currentIndex - 1];
    const currentValue = currentMonth[field];
    const previousValue = previousMonth[field] || 0;
    
    if (previousValue === 0) return null;
    
    const change = ((currentValue - previousValue) / previousValue) * 100;

    return {
      percentage: Math.abs(change),
      direction: change > 0 ? 'up' : 'down',
      amount: currentValue - previousValue
    };
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-400 mx-auto mb-4" />
          <p className="text-white">Loading history data...</p>
        </div>
      </div>
    );
  }

  const sortedHistory = [...history].sort((a, b) => {
    if (a.year !== b.year) return b.year - a.year;
    return new Date(`${b.month} 1, ${b.year}`).getMonth() - new Date(`${a.month} 1, ${a.year}`).getMonth();
  });

  if (history.length === 0) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-white">Financial History</h1>
          <p className="text-gray-400 mt-1">
            Monthly income, spending summaries and transaction history
          </p>
        </div>
        
        {error && (
          <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400">
            {error}
          </div>
        )}
        
        <div className="bg-gray-800 rounded-lg p-8 text-center">
          <Archive className="mx-auto h-16 w-16 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-white mb-2">No History Available</h3>
          <p className="text-gray-400">
            Complete months will automatically appear here as you add transaction data
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-white">Financial History</h1>
        <p className="text-gray-400 mt-1">
          Monthly income, spending summaries and detailed transaction history
        </p>
      </div>

      {error && (
        <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400">
          {error}
        </div>
      )}

      {!showTransactions ? (
        <>
          {/* History Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {sortedHistory.map((month) => {
              const spendingTrend = getTrendComparedToPrevious(month, 'totalSpent');
              const incomeTrend = getTrendComparedToPrevious(month, 'totalIncome');
              const netTrend = getTrendComparedToPrevious(month, 'netIncome');
              
              const topExpenseCategories = Object.entries(month.categories)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 2);
              
              const topIncomeCategories = Object.entries(month.incomeCategories || {})
                .sort(([,a], [,b]) => b - a)
                .slice(0, 2);

              return (
                <div key={`${month.year}-${month.month}`} className="bg-gray-800 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-blue-500/20 rounded-lg">
                        <Calendar className="text-blue-400" size={20} />
                      </div>
                      <div>
                        <h3 className="font-medium text-white">{month.month} {month.year}</h3>
                        <p className="text-sm text-gray-400">{month.transactions.length} transactions</p>
                      </div>
                    </div>
                    
                    <button
                      onClick={() => {
                        setSelectedMonth(month);
                        setShowTransactions(true);
                      }}
                      className="p-2 text-gray-400 hover:text-white transition-colors"
                    >
                      <Eye size={20} />
                    </button>
                  </div>

                  <div className="space-y-3">
                    {/* Income */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <DollarSign className="text-green-400" size={16} />
                        <span className="text-gray-400">Income</span>
                      </div>
                      <div className="text-right">
                        <span className="text-lg font-bold text-green-400">
                          ${(month.totalIncome || 0).toFixed(2)}
                        </span>
                        {incomeTrend && (
                          <div className={`flex items-center space-x-1 text-xs ${
                            incomeTrend.direction === 'up' ? 'text-green-400' : 'text-red-400'
                          }`}>
                            {incomeTrend.direction === 'up' ? (
                              <TrendingUp size={12} />
                            ) : (
                              <TrendingDown size={12} />
                            )}
                            <span>{incomeTrend.percentage.toFixed(1)}%</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Expenses */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <TrendingUp className="text-red-400" size={16} />
                        <span className="text-gray-400">Expenses</span>
                      </div>
                      <div className="text-right">
                        <span className="text-lg font-bold text-red-400">
                          ${month.totalSpent.toFixed(2)}
                        </span>
                        {spendingTrend && (
                          <div className={`flex items-center space-x-1 text-xs ${
                            spendingTrend.direction === 'up' ? 'text-red-400' : 'text-green-400'
                          }`}>
                            {spendingTrend.direction === 'up' ? (
                              <TrendingUp size={12} />
                            ) : (
                              <TrendingDown size={12} />
                            )}
                            <span>{spendingTrend.percentage.toFixed(1)}%</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Net Income */}
                    <div className="flex items-center justify-between pt-2 border-t border-gray-700">
                      <div className="flex items-center space-x-2">
                        <PiggyBank className={`${(month.netIncome || 0) >= 0 ? 'text-green-400' : 'text-red-400'}`} size={16} />
                        <span className="text-gray-400">Net</span>
                      </div>
                      <div className="text-right">
                        <span className={`text-lg font-bold ${
                          (month.netIncome || 0) >= 0 ? 'text-green-400' : 'text-red-400'
                        }`}>
                          ${(month.netIncome || 0).toFixed(2)}
                        </span>
                        {netTrend && (
                          <div className={`flex items-center space-x-1 text-xs ${
                            netTrend.direction === 'up' ? 'text-green-400' : 'text-red-400'
                          }`}>
                            {netTrend.direction === 'up' ? (
                              <TrendingUp size={12} />
                            ) : (
                              <TrendingDown size={12} />
                            )}
                            <span>{netTrend.percentage.toFixed(1)}%</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Top Categories */}
                    <div className="pt-3 border-t border-gray-700">
                      <p className="text-xs text-gray-500 mb-2">Top Categories</p>
                      <div className="space-y-1">
                        {topIncomeCategories.map(([category, amount]) => (
                          <div key={`income-${category}`} className="flex justify-between text-sm">
                            <span className="text-green-400 truncate">+{category}</span>
                            <span className="text-green-300">${amount.toFixed(0)}</span>
                          </div>
                        ))}
                        {topExpenseCategories.map(([category, amount]) => (
                          <div key={`expense-${category}`} className="flex justify-between text-sm">
                            <span className="text-red-400 truncate">-{category}</span>
                            <span className="text-red-300">${amount.toFixed(0)}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </>
      ) : selectedMonth && (
        <>
          {/* Transaction Details */}
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold text-white">
                {selectedMonth.month} {selectedMonth.year} Details
              </h2>
              <div className="flex items-center space-x-6 mt-1 text-sm">
                <span className="text-green-400">
                  Income: ${(selectedMonth.totalIncome || 0).toFixed(2)}
                </span>
                <span className="text-red-400">
                  Expenses: ${selectedMonth.totalSpent.toFixed(2)}
                </span>
                <span className={`${(selectedMonth.netIncome || 0) >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  Net: ${(selectedMonth.netIncome || 0).toFixed(2)}
                </span>
              </div>
            </div>
            
            <button
              onClick={() => {
                setShowTransactions(false);
                setSelectedMonth(null);
              }}
              className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
            >
              Back to History
            </button>
          </div>

          {/* Category Breakdown */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Income Categories */}
            {Object.keys(selectedMonth.incomeCategories || {}).length > 0 && (
              <div className="bg-gray-800 rounded-lg p-6">
                <h3 className="text-lg font-medium text-white mb-4 flex items-center space-x-2">
                  <DollarSign className="text-green-400" size={20} />
                  <span>Income Breakdown</span>
                </h3>
                <div className="space-y-3">
                  {Object.entries(selectedMonth.incomeCategories || {})
                    .sort(([,a], [,b]) => b - a)
                    .map(([category, amount]) => (
                      <div key={category} className="flex justify-between items-center">
                        <span className="text-gray-300">{category}</span>
                        <span className="text-green-400 font-medium">${amount.toFixed(2)}</span>
                      </div>
                    ))}
                </div>
              </div>
            )}

            {/* Expense Categories */}
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-lg font-medium text-white mb-4 flex items-center space-x-2">
                <TrendingUp className="text-red-400" size={20} />
                <span>Expense Breakdown</span>
              </h3>
              <div className="space-y-3">
                {Object.entries(selectedMonth.categories)
                  .sort(([,a], [,b]) => b - a)
                  .map(([category, amount]) => (
                    <div key={category} className="flex justify-between items-center">
                      <span className="text-gray-300">{category}</span>
                      <span className="text-red-400 font-medium">${amount.toFixed(2)}</span>
                    </div>
                  ))}
              </div>
            </div>
          </div>

          {/* Transactions Table */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-medium text-white mb-4">All Transactions</h3>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="text-gray-400 border-b border-gray-700">
                    <th className="text-left py-2">Date</th>
                    <th className="text-left py-2">Description</th>
                    <th className="text-left py-2">Category</th>
                    <th className="text-right py-2">Amount</th>
                  </tr>
                </thead>
                <tbody>
                  {selectedMonth.transactions
                    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
                    .map((transaction) => (
                      <tr key={transaction.id} className="border-b border-gray-700/50">
                        <td className="py-2 text-gray-300">
                          {new Date(transaction.date).toLocaleDateString()}
                        </td>
                        <td className="py-2 text-gray-300">{transaction.description}</td>
                        <td className="py-2 text-gray-400">{transaction.category}</td>
                        <td className={`py-2 text-right font-medium ${
                          transaction.amount < 0 ? 'text-red-400' : 'text-green-400'
                        }`}>
                          {transaction.amount < 0 ? '-' : '+'}${Math.abs(transaction.amount).toFixed(2)}
                        </td>
                      </tr>
                    ))}
                </tbody>
              </table>
            </div>
          </div>
        </>
      )}
    </div>
  );
}