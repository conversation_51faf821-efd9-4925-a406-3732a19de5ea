import { useState, useEffect } from 'react';
import { Transaction, OpenAIModel } from '../types';
import { storage } from '../utils/storage';
import { licenseManager } from '../utils/licenseManager';
import { openaiService } from '../utils/openai';
import { OPENAI_API_KEY } from '../utils/env';
import FileUpload from '../components/FileUpload';
import TransactionTable from '../components/TransactionTable';
import { AlertCircle, CheckCircle, Settings, Loader2, Shield, ExternalLink } from 'lucide-react';

export default function SpendingTab() {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [budgetCategories, setBudgetCategories] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [apiKey, setApiKey] = useState('');
  const [aiModel, setAiModel] = useState<OpenAIModel>('gpt-3.5-turbo-instruct');
  const [showApiKeyInput, setShowApiKeyInput] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const [transactionsData, categoriesData, settings] = await Promise.all([
        storage.getTransactions(),
        storage.getBudgetCategories(),
        storage.getSettings()
      ]);

      setTransactions(transactionsData);
      setBudgetCategories(categoriesData);
      
      // Check for API key in environment variables first
      const envApiKey = OPENAI_API_KEY;
      if (envApiKey) {
        // If found in environment, use it and update settings
        setApiKey(envApiKey);
        openaiService.setApiKey(envApiKey);
        if (!settings.apiKey) {
          // If the settings don't have the key yet, save it
          await storage.saveSettings({ ...settings, apiKey: envApiKey });
        }
      } else {
        // Fall back to stored API key and AI model
        setApiKey(settings.apiKey);
        setAiModel(settings.aiModel as OpenAIModel);
        openaiService.setApiKey(settings.apiKey);
        openaiService.setModel(settings.aiModel as OpenAIModel);
      }
    } catch (err) {
      console.error('Failed to load data:', err);
      setError('Failed to load transaction data. Please refresh the page.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileUpload = async (csvContent: string, fileName: string) => {
    if (!apiKey) {
      setError('Please set your OpenAI API key first');
      setShowApiKeyInput(true);
      return;
    }

    // Check license restrictions
    if (!licenseManager.canUploadUnlimitedData()) {
      // Count estimated transactions
      const estimatedTransactions = csvContent.split('\n').length - 1;
      const existingTransactions = await storage.getTransactions();
      
      // Check if adding new data would exceed 3-month limit
      const dataCheck = licenseManager.isDataWithinLimits([...existingTransactions]);
      if (!dataCheck.withinLimits) {
        setError(`${dataCheck.message} Please activate your license to upload unlimited data.`);
        return;
      }

      // Warn about upcoming limit
      if (estimatedTransactions > 1000) {
        setError('Unlicensed version limited to smaller data uploads. Please activate your license for unlimited uploads.');
        return;
      }
    }

    // Check file size (limit to ~1MB of text)
    if (csvContent.length > 1000000) {
      setError('File is too large. Please reduce the data timeframe or split into smaller files.');
      return;
    }

    // Count estimated transactions
    const estimatedTransactions = csvContent.split('\n').length - 1;
    if (estimatedTransactions > 5000) {
      setError('Too many transactions. Please limit to 5000 transactions per upload.');
      return;
    }

    setIsUploading(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await openaiService.categorizeTransactions(csvContent);
      
      if (!response.categorized || !Array.isArray(response.categorized)) {
        throw new Error('Invalid response format from OpenAI');
      }

      const newTransactions: Transaction[] = response.categorized.map((item, index) => ({
        id: `${Date.now()}-${index}`,
        date: item.date, // AI should already return YYYY-MM-DD format based on our instructions
        description: item.description,
        category: item.category,
        amount: item.amount,
        originalData: csvContent
      }));

      const result = await storage.addTransactions(newTransactions) as any;
      const updatedTransactions = await storage.getTransactions();
      
      // Check license limits after upload
      const dataCheck = licenseManager.isDataWithinLimits(updatedTransactions);
      if (!dataCheck.withinLimits) {
        setError(`Warning: ${dataCheck.message}`);
      }
      
      setTransactions(updatedTransactions);
      
      // Update budget categories with current spending
      const updatedBudgetCategories = await storage.getBudgetCategories();
      const categoriesWithSpending = updatedBudgetCategories.map(category => ({
        ...category,
        currentSpending: category.type === 'expense'
          ? updatedTransactions
              .filter(t => t.category === category.name && t.amount < 0)
              .reduce((sum, t) => sum + Math.abs(t.amount), 0)
          : updatedTransactions
              .filter(t => t.category === category.name && t.amount > 0)
              .reduce((sum, t) => sum + t.amount, 0)
      }));
      
      setBudgetCategories(categoriesWithSpending);
      
      let successMessage = `Successfully processed ${fileName}`;
      if (result && typeof result === 'object') {
        successMessage += ` - Added: ${result.added} transactions`;
        if (result.skipped > 0) {
          successMessage += `, Skipped ${result.skipped} duplicates`;
        }
        if (result.internalTransfers > 0) {
          successMessage += `, Detected ${result.internalTransfers} internal transfers`;
        }
        if (result.monthsProcessed > 0) {
          successMessage += `, Organized into ${result.monthsProcessed} months`;
        }
      } else {
        successMessage += ` - ${newTransactions.length} transactions`;
      }
      
      setSuccess(successMessage);
    } catch (err) {
      console.error('Upload error:', err);
      let errorMessage = 'Failed to process CSV file';
      
      if (err instanceof Error) {
        if (err.message.includes('API key')) {
          errorMessage = 'Invalid API key. Please check your OpenAI API key.';
        } else if (err.message.includes('quota')) {
          errorMessage = 'OpenAI API quota exceeded. Please check your account.';
        } else if (err.message.includes('rate limit')) {
          errorMessage = 'Rate limit exceeded. Please wait a moment and try again.';
        } else {
          errorMessage = err.message;
        }
      }
      
      setError(errorMessage);
    } finally {
      setIsUploading(false);
    }
  };

  const handleApiSettingsSubmit = async () => {
    try {
      const settings = await storage.getSettings();
      await storage.saveSettings({ ...settings, apiKey, aiModel });
      openaiService.setApiKey(apiKey);
      openaiService.setModel(aiModel);
      setShowApiKeyInput(false);
      setSuccess('API settings saved successfully');
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      setError('Failed to save API settings');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-400 mx-auto mb-4" />
          <p className="text-white">Loading transactions...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Transaction Spending</h1>
          <p className="text-gray-400 mt-1">
            Upload your bank transaction CSV files for automatic categorization
          </p>
          {!licenseManager.canUploadUnlimitedData() && (
            <div className="flex items-center space-x-2 mt-2">
              <Shield className="text-yellow-400" size={16} />
              <span className="text-yellow-400 text-sm">
                Unlicensed: Limited to 3 months of data
              </span>
            </div>
          )}
        </div>
        <button
          onClick={() => setShowApiKeyInput(!showApiKeyInput)}
          className="flex items-center space-x-2 px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
        >
          <Settings size={16} />
          <span>API Settings</span>
        </button>
      </div>

      {showApiKeyInput && (
        <div className="bg-gray-800 rounded-lg p-4">
          <h3 className="text-lg font-medium text-white mb-3">OpenAI API Configuration</h3>
          
          {OPENAI_API_KEY ? (
            <div className="mb-3 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
              <div className="flex items-center space-x-2">
                <ExternalLink size={18} className="text-blue-400" />
                <span className="text-blue-400 font-medium">API key loaded from environment</span>
              </div>
              <p className="text-xs text-gray-400 mt-1">
                Your OpenAI API key was loaded from the .env file. You can still override it below if needed.
              </p>
            </div>
          ) : null}
          
          <div className="flex space-x-3">
            <input
              type="password"
              placeholder="Enter your OpenAI API key"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button
              onClick={handleApiSettingsSubmit}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              Save
            </button>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Your API key is stored locally and never sent to our servers. For better security, set it in the .env file.
          </p>
          
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-300 mb-2">OpenAI Model</label>
            <div className="relative">
              <select
                value={aiModel}
                onChange={(e) => setAiModel(e.target.value as OpenAIModel)}
                className="block w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none"
              >
                <optgroup label="Completions Models (Recommended)">
                  <option value="gpt-3.5-turbo-instruct">GPT-3.5 Turbo Instruct (Recommended)</option>
                  <option value="text-davinci-003">Text Davinci 003</option>
                  <option value="text-davinci-002">Text Davinci 002</option>
                </optgroup>
                <optgroup label="Chat Models">
                  <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                  <option value="gpt-3.5-turbo-0125">GPT-3.5 Turbo 0125</option>
                  <option value="gpt-4">GPT-4</option>
                  <option value="gpt-4-0125-preview">GPT-4 0125 Preview</option>
                  <option value="gpt-4-turbo">GPT-4 Turbo</option>
                  <option value="gpt-4-turbo-preview">GPT-4 Turbo Preview</option>
                  <option value="gpt-4-1106-preview">GPT-4 1106 Preview</option>
                </optgroup>
              </select>
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </div>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Select the OpenAI model to use for transaction categorization. More advanced models may offer better accuracy but cost more and could be slower.
            </p>
          </div>
        </div>
      )}

      {error && (
        <div className="flex items-center space-x-2 p-4 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400">
          <AlertCircle size={20} />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="flex items-center space-x-2 p-4 bg-green-500/10 border border-green-500/20 rounded-lg text-green-400">
          <CheckCircle size={20} />
          <span>{success}</span>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-1">
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-lg font-medium text-white mb-4">Upload Transactions</h2>
            <FileUpload onFileUpload={handleFileUpload} isLoading={isUploading} />
            
            {isUploading && (
              <div className="mt-4 flex items-center space-x-2 text-blue-400">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm">Processing with AI...</span>
              </div>
            )}
          </div>
        </div>

        <div className="lg:col-span-2">
          <div className="bg-gray-800 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-medium text-white">Recent Transactions</h2>
              <span className="text-sm text-gray-400">
                {transactions.length} transactions
              </span>
            </div>
            <TransactionTable 
              transactions={transactions} 
              budgetCategories={budgetCategories}
            />
          </div>
        </div>
      </div>
    </div>
  );
}