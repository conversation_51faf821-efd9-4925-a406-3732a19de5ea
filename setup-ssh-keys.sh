#!/bin/bash

# Standalone SSH Key Setup Script for FocusBudget Remote Deployment
# This script sets up SSH key authentication for passwordless deployments

# Colors for better readability
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

# Configuration
REMOTE_USER="labmaster"
REMOTE_HOST="**********"

echo -e "\n${BLUE}==============================="
echo -e " SSH Key Setup for FocusBudget"
echo -e "===============================${NC}\n"

echo -e "${YELLOW}This script will set up SSH key authentication for:${NC}"
echo -e "${YELLOW}  • Remote user: ${REMOTE_USER}${NC}"
echo -e "${YELLOW}  • Remote host: ${REMOTE_HOST}${NC}"
echo -e "${YELLOW}  • This will enable passwordless deployments${NC}\n"

# Check if sshpass is installed
if ! command -v sshpass >/dev/null 2>&1; then
    echo -e "${YELLOW}[i] Installing sshpass for password authentication...${NC}"
    
    if command -v apt-get >/dev/null 2>&1; then
        sudo apt-get update && sudo apt-get install -y sshpass
    elif command -v yum >/dev/null 2>&1; then
        sudo yum install -y sshpass
    elif command -v dnf >/dev/null 2>&1; then
        sudo dnf install -y sshpass
    else
        echo -e "${RED}[!] Could not install sshpass. Please install it manually.${NC}"
        exit 1
    fi
fi

# Check if SSH key already exists and works
if [ -f "$HOME/.ssh/id_rsa.pub" ]; then
    echo -e "${YELLOW}[i] Found existing SSH key, testing...${NC}"
    if ssh -o ConnectTimeout=10 -o BatchMode=yes -o PasswordAuthentication=no -o StrictHostKeyChecking=no "${REMOTE_USER}@${REMOTE_HOST}" exit 2>/dev/null; then
        echo -e "${GREEN}[✓] SSH key authentication already working!${NC}"
        echo -e "${GREEN}[✓] No setup needed${NC}"
        exit 0
    else
        echo -e "${YELLOW}[i] SSH key exists but not authorized on remote server${NC}"
    fi
else
    echo -e "${YELLOW}[i] No SSH key found, will generate one${NC}"
fi

# Get password for remote server
echo -e "${BLUE}[>] Setting up SSH key authentication${NC}"
read -s -p "Enter password for ${REMOTE_USER}@${REMOTE_HOST}: " REMOTE_PASSWORD
echo

if [ -z "$REMOTE_PASSWORD" ]; then
    echo -e "${RED}[!] Password cannot be empty${NC}"
    exit 1
fi

# Test password authentication
echo -e "${BLUE}[>] Testing password authentication${NC}"
if ! sshpass -p "$REMOTE_PASSWORD" ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no "${REMOTE_USER}@${REMOTE_HOST}" exit 2>/dev/null; then
    echo -e "${RED}[!] Password authentication failed${NC}"
    exit 1
fi

echo -e "${GREEN}[✓] Password authentication working${NC}"

# Generate SSH key if it doesn't exist
if [ ! -f "$HOME/.ssh/id_rsa" ]; then
    echo -e "${BLUE}[>] Generating SSH key pair${NC}"
    
    # Create .ssh directory if it doesn't exist
    mkdir -p "$HOME/.ssh"
    chmod 700 "$HOME/.ssh"
    
    # Generate SSH key
    ssh-keygen -t rsa -b 4096 -f "$HOME/.ssh/id_rsa" -N "" -C "$(whoami)@$(hostname)-focusbudget"
    
    echo -e "${GREEN}[✓] SSH key generated${NC}"
else
    echo -e "${GREEN}[✓] SSH key already exists${NC}"
fi

# Set proper permissions
chmod 600 "$HOME/.ssh/id_rsa" 2>/dev/null || true
chmod 644 "$HOME/.ssh/id_rsa.pub" 2>/dev/null || true

# Start ssh-agent if not running
if ! ssh-add -l >/dev/null 2>&1; then
    echo -e "${BLUE}[>] Starting SSH agent${NC}"
    eval "$(ssh-agent -s)"
    ssh-add "$HOME/.ssh/id_rsa" 2>/dev/null || true
else
    echo -e "${GREEN}[✓] SSH agent is running${NC}"
fi

# Copy SSH key to remote server
echo -e "${BLUE}[>] Copying SSH key to remote server${NC}"
if sshpass -p "$REMOTE_PASSWORD" ssh-copy-id -o StrictHostKeyChecking=no -i "$HOME/.ssh/id_rsa.pub" "${REMOTE_USER}@${REMOTE_HOST}"; then
    echo -e "${GREEN}[✓] SSH key copied successfully${NC}"
else
    echo -e "${RED}[!] Failed to copy SSH key${NC}"
    exit 1
fi

# Test SSH key authentication
echo -e "${BLUE}[>] Testing SSH key authentication${NC}"
sleep 2  # Give the server a moment to process the key

if ssh -o ConnectTimeout=10 -o BatchMode=yes -o PasswordAuthentication=no -o StrictHostKeyChecking=no "${REMOTE_USER}@${REMOTE_HOST}" exit 2>/dev/null; then
    echo -e "${GREEN}[✓] SSH key authentication working!${NC}"
    echo -e "${GREEN}[✓] Future deployments will be passwordless${NC}"
    
    # Test with a simple command
    echo -e "${BLUE}[>] Testing remote command execution${NC}"
    if ssh "${REMOTE_USER}@${REMOTE_HOST}" "echo 'SSH key test successful'" 2>/dev/null; then
        echo -e "${GREEN}[✓] Remote command execution working${NC}"
    else
        echo -e "${YELLOW}[i] SSH key works but command execution may have issues${NC}"
    fi
    
    echo -e "\n${GREEN}==============================="
    echo -e " SSH Key Setup Complete!"
    echo -e "===============================${NC}"
    echo -e "${GREEN}[✓] SSH key authentication configured${NC}"
    echo -e "${GREEN}[✓] Future deployments will be passwordless${NC}"
    echo -e "${YELLOW}[i] You can now run ./remote-deploy.sh without passwords${NC}"
    
else
    echo -e "${RED}[!] SSH key authentication test failed${NC}"
    echo -e "${YELLOW}[i] The key was copied but may not be working yet${NC}"
    echo -e "${YELLOW}[i] Try running this script again or check remote server configuration${NC}"
    exit 1
fi
